.CUTlist {
    overflow: auto;
}

.CUTlist .data-table {
    font-size: 13px;
}

.CUTlist .data-table th:nth-child(1),
.CUTlist .data-table td:nth-child(1) {
    position: sticky;
    left: 0;
    z-index: 2;
    min-width: 60px;
    background-color: #f5f7fa;
}

.CUTlist .data-table th:nth-child(2),
.CUTlist .data-table td:nth-child(2) {
    position: sticky;
    left: 60px;
    z-index: 2;
    min-width: 55px;
    background-color: #f5f7fa;
}

.CUTlist .data-table th:nth-child(3),
.CUTlist .data-table td:nth-child(3) {
    position: sticky;
    left: 115px;
    z-index: 2;
    min-width: 75px;
}
.CUTlist .data-table th:nth-child(4),
.CUTlist .data-table td:nth-child(4) {
    position: sticky;
    left: 190px;
    z-index: 2;
    min-width: 65px;
}
.CUTlist .data-table th:nth-child(5),
.CUTlist .data-table td:nth-child(5) {
    position: sticky;
    left: 255px;
    z-index: 2;
    min-width: 55px;
}


.CUTlist .data-table th {
    padding: 12px 0px;
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: #f5f7fa;
}

.CUTlist .data-table td:nth-child(1),
.CUTlist .data-table td:nth-child(2),
.CUTlist .data-table td:nth-child(3),
.CUTlist .data-table td:nth-child(4),
.CUTlist .data-table td:nth-child(5) {
    padding: 0px;
}

/* 处理固定行列交叉处的单元格 */
.CUTlist .data-table th:nth-child(1),
.CUTlist .data-table th:nth-child(2),
.CUTlist .data-table th:nth-child(3),
.CUTlist .data-table th:nth-child(4),
.CUTlist .data-table th:nth-child(5){
    z-index: 3;
    /* 确保交叉点的表头单元格在最上层 */
    border-bottom: 1px solid #ddd;
}

.CUTlist .data-table td,
.CUTlist .data-table th {
    min-width: 50px;
    /* padding: 12px; */
    border: 1px solid #ddd;
    text-align: center;
}

.CUTlist .data-table tr:hover {
    background-color: #f9fafb;
}

.inventory-cell {
    background-color: #f0f9eb;
    font-weight: bold;
}

.loading-spinner {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 3px solid #f3f3f3;
    border-radius: 50%;
    border-top-color: #3498db;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.highlight {
    background-color: rgb(255, 214, 220);
    font-weight: bold;
    color: red;
}

/* 可点击单元格样式 */
.clickable-cell {
    cursor: pointer;
}

.cut-cell {
    font-weight: bold;
    color: #003366;
    text-decoration: underline;
}

.clickable-cell:hover {
    background-color: #e6f7ff;
}

/* 搜索工具栏样式优化 */
.search-toolbar {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 12px 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.search-toolbar label {
    font-weight: bold;
    color: #333;
    white-space: nowrap;
}

.search-toolbar .ant-input {
    width: 200px;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.search-toolbar .ant-input:focus {
    border-color: #1890ff;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-toolbar .ant-btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    white-space: nowrap;
}

.search-toolbar .ant-btn-primary {
    background-color: #1890ff;
    color: #fff;
}

.search-toolbar .ant-btn-default {
    background-color: #bfbfbf;
    color: #fff;
}

.search-toolbar .ant-btn:hover {
    opacity: 0.8;
}

.search-toolbar .ant-btn-primary:hover {
    background-color: #40a9ff;
}

.search-toolbar .ant-btn-default:hover {
    background-color: #8c8c8c;
}

.search-toolbar .ant-btn:focus {
    outline: 2px solid #1890ff;
    outline-offset: 2px;
}

/* 添加按钮容器，使按钮右对齐 */
.search-toolbar .button-group {
    margin-left: auto;
    display: flex;
    gap: 8px;
}

/* LINE-MODEL-JIG表格样式 */
.line-model-jig-section {
    background-color: #fafafa;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.line-model-jig-table-container {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.line-model-jig-table {
    font-size: 13px;
    width: 100%;
    border-collapse: collapse;
    background-color: #fff;
}

.line-model-jig-table th {
    background-color: #fafafa;
    border: 1px solid #f0f0f0;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    color: #262626;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 1;
}

.line-model-jig-table td {
    border: 1px solid #f0f0f0;
    padding: 8px;
    text-align: center;
    color: #595959;
    transition: background-color 0.3s ease;
}

.line-model-jig-table tr:hover td {
    background-color: #f5f5f5;
}

.line-model-jig-table tr:nth-child(even) {
    background-color: #fafafa;
}

.line-model-jig-table tr:nth-child(even):hover td {
    background-color: #f0f0f0;
}

/* 响应式设计 - 小屏幕适配 */
@media screen and (max-width: 768px) {
    .line-model-jig-section {
        padding: 8px;
        margin-top: 12px;
    }

    .line-model-jig-table-container {
        height: 250px;
    }

    .line-model-jig-table th,
    .line-model-jig-table td {
        padding: 6px 4px;
        font-size: 12px;
    }

    .line-model-jig-table th:nth-child(1) { width: 60px; }
    .line-model-jig-table th:nth-child(2) { width: 100px; }
    .line-model-jig-table th:nth-child(3) { width: 80px; }
}

/* 滚动条样式优化 */
.line-model-jig-table-container::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.line-model-jig-table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.line-model-jig-table-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.line-model-jig-table-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 表格加载状态样式 */
.line-model-jig-table .loading-row td {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
}

/* 表格空状态样式 */
.line-model-jig-table .empty-row td {
    text-align: center;
    padding: 20px;
    color: #999;
    background-color: #fafafa;
}
