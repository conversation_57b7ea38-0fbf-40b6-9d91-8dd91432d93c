document.addEventListener("DOMContentLoaded", function () {
  loadDemandTable();
});

// 保存原始数据用于重置
let originalMatrix = [];
let currentMatrix = [];

async function loadDemandTable() {
  const table = document.querySelector(".data-table");
  try {
    showLoading(table);
    const response = await fetch("get_cut.php");
    const { demand, inventory, able } = await response.json();
    const { dates, cuts, matrix } = transformDemandData(
      demand,
      inventory,
      able
    );

    // 保存原始数据
    originalMatrix = matrix;
    currentMatrix = [...matrix];

    renderDemandTable(dates, cuts, currentMatrix);

    // 初始化搜索功能
    initSearchFunctionality(dates, cuts);
  } catch (error) {
    console.error("数据加载失败:", error);
    showError(table, error);
  }
}

function initSearchFunctionality(dates, cuts) {
  const searchInput = document.getElementById("cutSearch");
  const searchBtn = document.getElementById("searchBtn");
  const resetBtn = document.getElementById("resetBtn");

  // 查询按钮事件
  searchBtn.addEventListener("click", () => {
    performSearch(dates, cuts);
  });

  // 回车键事件
  searchInput.addEventListener("keypress", (e) => {
    if (e.key === "Enter") {
      performSearch(dates, cuts);
    }
  });

  // 重置按钮事件
  resetBtn.addEventListener("click", () => {
    searchInput.value = "";
    currentMatrix = [...originalMatrix];
    renderDemandTable(dates, cuts, currentMatrix);
  });
}

function performSearch(dates, cuts) {
  const searchValue = document
    .getElementById("cutSearch")
    .value.trim()
    .toLowerCase();

  if (searchValue === "") {
    // 如果搜索值为空，显示所有数据
    currentMatrix = [...originalMatrix];
  } else {
    // 筛选数据，使用模糊匹配（不区分大小写）
    currentMatrix = originalMatrix.filter((row) =>
      row.cut.toLowerCase().includes(searchValue)
    );
  }

  renderDemandTable(dates, cuts, currentMatrix);
}

function transformDemandData(demandData, inventoryData, ableData) {
  const dates = [...new Set(demandData.map((d) => d.rdate))].sort(
    (a, b) => new Date(a) - new Date(b)
  );
  const cuts = [...new Set(demandData.map((d) => d.CUT))].sort((a, b) =>
    a.localeCompare(b)
  );

  const matrix = cuts.map((cut) => {
    const row = {
      cut,
      inventory: inventoryData[cut] || 0,
      able: ableData[cut] || 0,
    };
    const demands = [];
    dates.forEach((date) => {
      const demand = demandData.find((d) => d.CUT === cut && d.rdate === date);
      const value = demand ? parseInt(demand.TotalDemand, 10) : 0;
      row[date] = value;
      demands.push(value);
    });
    const sortedDemands = [...demands].sort((a, b) => b - a);
    row.maxDemand = sortedDemands[0] || 0;
    return row;
  });

  return { dates, cuts, matrix };
}

function renderDemandTable(dates, cuts, matrix) {
  const table = document.querySelector(".data-table");
  table.innerHTML = "";

  // 将日期格式化为 MM/DD 格式
  const formattedDates = dates.map((date) => {
    const d = new Date(date);
    const month = String(d.getMonth() + 1).padStart(2, "0");
    const day = String(d.getDate()).padStart(2, "0");
    return `${month}/${day}`;
  });

  const thead = document.createElement("thead");
  thead.innerHTML = `
        <tr>
            <th>Cut</th>
            <th>Total</th>
            <th>Inventory</th>
            <th>MaxPlan</th>
            <th>Gap</th>
            ${formattedDates.map((date) => `<th>${date}</th>`).join("")}
        </tr>
    `;
  table.appendChild(thead);

  const tbody = document.createElement("tbody");
  matrix.forEach((rowData) => {
    const tr = document.createElement("tr");
    tr.innerHTML = `
            <td class="cut-cell clickable-cell" data-cut="${rowData.cut}">${
      rowData.cut
    }</td>
            <td class="inventory-cell">${rowData.inventory}</td>
            <td class="inventory-cell">${rowData.able}</td>
            <td class="inventory-cell">${rowData.maxDemand}</td>
            <td class=" ${
              rowData.inventory - rowData.maxDemand < 0
                ? "highlight"
                : "inventory-cell"
            }">${rowData.inventory - rowData.maxDemand}</td>
            ${dates
              .map(
                (date) => `
                <td class="${
                  rowData[date] > rowData.inventory ? "highlight" : ""
                }">
                    ${rowData[date]}
                </td>
            `
              )
              .join("")}
        `;
    tbody.appendChild(tr);
  });
  table.appendChild(tbody);

  // 添加点击事件监听器
  table.addEventListener("click", async function (e) {
    if (e.target.classList.contains("clickable-cell")) {
      const cut = e.target.getAttribute("data-cut");
      await showCutDetails(cut);
    }
  });
}

async function showCutDetails(cut) {
  // 创建模态框
  const modal = document.createElement("div");
  modal.className = "modal";
  modal.id = "cutDetailsModal";
  modal.style.cssText = `
    display: block;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
  `;

  // 获取数据
  let cutData = [];
  try {
    const response = await fetch(
      `get_cut_details.php?cut=${encodeURIComponent(cut)}`
    );
    const result = await response.json();
    if (result.success) {
      cutData = result.data;
    }
  } catch (error) {
    console.error("获取数据失败:", error);
  }

  let sortDirection = "none"; // 'none', 'asc', 'desc'

  // 排序函数
  function sortTable(data, direction) {
    if (direction === "none") {
      return data;
    }

    return [...data].sort((a, b) => {
      const aStatue = a.statue || "";
      const bStatue = b.statue || "";

      if (direction === "asc") {
        return aStatue.localeCompare(bStatue);
      } else {
        return bStatue.localeCompare(aStatue);
      }
    });
  }

  // 渲染表格函数
  function renderTable(data) {
    const tbody = modal.querySelector(".detail-table tbody");
    if (!tbody) return;

    tbody.innerHTML = "";

    if (data.length === 0) {
      const row = document.createElement("tr");
      row.innerHTML =
        '<td colspan="4" style="border: 1px solid #ddd; padding: 12px; text-align: center;">暂无数据</td>';
      tbody.appendChild(row);
      return;
    }

    data.forEach((item) => {
      const row = document.createElement("tr");
      row.innerHTML = `
        <td style="border: 1px solid #ddd; padding: 6px; text-align: center;">${item.CUTID}</td>
        <td style="border: 1px solid #ddd; padding: 6px; text-align: center;">${item.CUTLINE}</td>
        <td style="border: 1px solid #ddd; padding: 6px; text-align: center;">${item.statue}</td>
        <td style="border: 1px solid #ddd; padding: 6px; text-align: center;">${item.smod}</td>
      `;
      tbody.appendChild(row);
    });
  }

  // 模态框内容
  modal.innerHTML = `
    <div class="modal-content" style="
      background-color: #fefefe;
      margin: 5% auto;
      padding: 20px;
      border: 1px solid #888;
      width: 80%;
      max-width: 800px;
      max-height: 800px;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      display: flex;
      flex-direction: column;
    ">
      <div class="modal-header" style="
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #eee;
      ">
        <h2>${cut} 详细信息</h2>
        <span class="close" style="
          color: #aaa;
          font-size: 28px;
          font-weight: bold;
          cursor: pointer;
        ">&times;</span>
      </div>
      <div class="modal-body" style="flex: 1; overflow: hidden;">
        <div style="max-height: 450px; overflow-y: auto;">
          <table class="detail-table" style="
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 13px;
            padding: 10px;
          ">
            <thead>
              <tr style="background-color: #f5f7fa;">
                <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">CUTID</th>
                <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">CUTLINE</th>
                <th id="statue-header" style="border: 1px solid #ddd; padding: 10px; text-align: center; cursor: pointer;">
                  STATUE <span id="sort-indicator"></span>
                </th>
                <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">SMOD</th>
              </tr>
            </thead>
            <tbody>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  `;

  // 初始化表格
  const sortedData = sortTable(cutData, sortDirection);
  renderTable(sortedData);

  // 添加排序点击事件
  const statueHeader = modal.querySelector("#statue-header");
  const sortIndicator = modal.querySelector("#sort-indicator");

  statueHeader.addEventListener("click", () => {
    // 更新排序方向
    if (sortDirection === "none") {
      sortDirection = "asc";
    } else if (sortDirection === "asc") {
      sortDirection = "desc";
    } else {
      sortDirection = "none";
    }

    if (sortDirection === "asc") {
      sortIndicator.textContent = "▲";
    } else if (sortDirection === "desc") {
      sortIndicator.textContent = "▼";
    } else {
      sortIndicator.textContent = "";
    }

    const sortedData = sortTable(cutData, sortDirection);
    renderTable(sortedData);
  });

  // 添加关闭事件
  modal.querySelector(".close").onclick = function () {
    document.body.removeChild(modal);
  };

  // 点击模态框外部关闭
  modal.onclick = function (event) {
    if (event.target === modal) {
      document.body.removeChild(modal);
    }
  };

  document.body.appendChild(modal);
}

function showLoading(table) {
  table.innerHTML = `
        <tr>
            <td colspan="100%" style="text-align: center; padding: 40px;">
                <div class="loading-spinner"></div>
                <div>数据加载中...</div>
            </td>
        </tr>
    `;
}

function showError(table, error) {
  table.innerHTML = `
        <tr>
            <td colspan="100%" style="color: red; padding: 20px;">
                数据加载失败：${error.message}
            </td>
        </tr>
    `;
}
