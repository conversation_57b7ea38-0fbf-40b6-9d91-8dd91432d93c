// JIG数据查询相关功能
document.addEventListener("DOMContentLoaded", function () {
    initJigFunctionality();
});

function initJigFunctionality() {
    const jigSearchBtn = document.getElementById("jigSearchBtn");
    const jigResetBtn = document.getElementById("jigResetBtn");
    const lineSearch = document.getElementById("lineSearch");
    const modelSearch = document.getElementById("modelSearch");
    const jigSearch = document.getElementById("jigSearch");

    // 查询按钮事件
    if (jigSearchBtn) {
        jigSearchBtn.addEventListener("click", () => {
            performJigSearch();
        });
    }

    // 重置按钮事件
    if (jigResetBtn) {
        jigResetBtn.addEventListener("click", () => {
            resetJigSearch();
        });
    }

    // 回车键事件
    [lineSearch, modelSearch, jigSearch].forEach(input => {
        if (input) {
            input.addEventListener("keypress", (e) => {
                if (e.key === "Enter") {
                    performJigSearch();
                }
            });
        }
    });
}

async function performJigSearch() {
    const lineValue = document.getElementById("lineSearch").value.trim();
    const modelValue = document.getElementById("modelSearch").value.trim();
    const jigValue = document.getElementById("jigSearch").value.trim();
    
    const tbody = document.querySelector(".line-model-jig-table tbody");
    
    try {
        // 显示加载状态
        showJigLoading(tbody);
        
        // 构建查询参数
        const params = new URLSearchParams();
        if (lineValue) params.append('line', lineValue);
        if (modelValue) params.append('model', modelValue);
        if (jigValue) params.append('jig', jigValue);
        
        // 发送请求
        const response = await fetch(`get_jig_data.php?${params.toString()}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            renderJigTable(result.data);
            
            // 显示查询结果统计
            const message = result.count > 0 
                ? `查询成功，共找到 ${result.count} 条记录`
                : '未找到匹配的数据';
            showJigMessage(message, result.count > 0 ? 'success' : 'info');
        } else {
            throw new Error(result.message || '查询失败');
        }
        
    } catch (error) {
        console.error("JIG查询失败:", error);
        showJigError(tbody, error.message);
        showJigMessage(`查询失败: ${error.message}`, 'error');
    }
}

function resetJigSearch() {
    // 清空搜索表单
    document.getElementById("lineSearch").value = "";
    document.getElementById("modelSearch").value = "";
    document.getElementById("jigSearch").value = "";
    
    // 重置表格为默认状态
    const tbody = document.querySelector(".line-model-jig-table tbody");
    tbody.innerHTML = `
        <tr class="default-message">
            <td colspan="3" style="text-align: center; padding: 20px; color: #999; font-style: italic;">
                请点击查询按钮获取数据
            </td>
        </tr>
    `;
    
    showJigMessage('已重置搜索条件', 'info');
}

function renderJigTable(data) {
    const tbody = document.querySelector(".line-model-jig-table tbody");
    tbody.innerHTML = "";
    
    if (data.length === 0) {
        tbody.innerHTML = `
            <tr class="empty-row">
                <td colspan="3" style="text-align: center; padding: 20px; color: #999; background-color: #fafafa;">
                    未找到匹配的数据
                </td>
            </tr>
        `;
        return;
    }
    
    data.forEach((item, index) => {
        const tr = document.createElement("tr");
        tr.innerHTML = `
            <td>${escapeHtml(item.LINE || '')}</td>
            <td>${escapeHtml(item.MODEL || '')}</td>
            <td>${escapeHtml(item.JIG || '')}</td>
        `;
        
        // 添加斑马纹效果
        if (index % 2 === 0) {
            tr.style.backgroundColor = '#fafafa';
        }
        
        tbody.appendChild(tr);
    });
}

function showJigLoading(tbody) {
    tbody.innerHTML = `
        <tr class="loading-row">
            <td colspan="3" style="text-align: center; padding: 20px;">
                <div class="loading-spinner"></div>
                <div style="margin-top: 10px; color: #666;">正在查询数据...</div>
            </td>
        </tr>
    `;
}

function showJigError(tbody, errorMessage) {
    tbody.innerHTML = `
        <tr class="error-row">
            <td colspan="3" style="text-align: center; padding: 20px; color: #ff4d4f; background-color: #fff2f0;">
                <div style="font-weight: bold;">查询失败</div>
                <div style="margin-top: 5px; font-size: 12px;">${escapeHtml(errorMessage)}</div>
            </td>
        </tr>
    `;
}

function showJigMessage(message, type = 'info') {
    // 创建消息提示
    const messageDiv = document.createElement('div');
    messageDiv.className = `jig-message jig-message-${type}`;
    messageDiv.textContent = message;
    
    // 设置样式
    const styles = {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '4px',
        color: '#fff',
        fontWeight: 'bold',
        zIndex: '9999',
        maxWidth: '300px',
        wordWrap: 'break-word',
        boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
    };
    
    // 根据类型设置背景色
    const colors = {
        success: '#52c41a',
        error: '#ff4d4f',
        info: '#1890ff',
        warning: '#faad14'
    };
    
    Object.assign(messageDiv.style, styles);
    messageDiv.style.backgroundColor = colors[type] || colors.info;
    
    // 添加到页面
    document.body.appendChild(messageDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}

// HTML转义函数，防止XSS攻击
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// 导出函数供其他脚本使用
window.jigFunctions = {
    performJigSearch,
    resetJigSearch,
    renderJigTable,
    showJigMessage
};
