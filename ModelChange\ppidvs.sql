-- step ppid
delete from [product].[dbo].[ppid]
insert into [product].[dbo].[ppid]
SELECT m.*,o.CPPPID as CP_RECIPEID from
(SELECT distinct left([eqpid],1) AS MOD
      ,[model] AS PRODUCT
	  ,MAX(case when [moduleid] ='OLBSI_RECIPEID' then [ppid] end ) AS OLBSI_RECIPEID
	  ,MAX(case when [moduleid] ='SI_RECIPEID' then [ppid] end ) AS SI_RECIPEID
	  ,MAX(case when [moduleid] ='SS_RECIPEID' then [ppid] end ) AS SS_RECIPEID
	  ,case when MAX(case when [moduleid] ='PB01_RECIPEID' and len([ppid])>0  then [ppid] else '-'  end ) ='-' then MAX(case when [moduleid] ='PB02_RECIPEID' and len([ppid])>0  then [ppid] else '-'  end )
	  when  substring(MAX(case when [moduleid] ='PB01_RECIPEID' and len([ppid])>0  then [ppid] else '-'  end ),13,2)='XR' THEN MAX(case when [moduleid] ='PB02_RECIPEID' then [ppid] end )
	  else MAX(case when [moduleid] ='PB01_RECIPEID' and len([ppid])>0  then [ppid] else '-'  end ) end AS PB01_RECIPEID
	  ,case when MAX(case when [moduleid] ='PB01_RECIPEID' and len([ppid])>0  then [ppid] else '-'  end )='-' then '-' 
			when MAX(case when [moduleid] ='PB01_RECIPEID' then [ppid] end )=MAX(case when [moduleid] ='PB02_RECIPEID' then [ppid] end ) then '-' 
			WHEN  substring(MAX(case when [moduleid] ='PB02_RECIPEID' and len([ppid])>0  then [ppid] else '-'  end ),13,2)='XL'  OR substring(MAX(case when [moduleid] ='PB02_RECIPEID' and len([ppid])>0  then [ppid] else '-'  end ),13,2)='XC'
			     THEN  MAX(case when [moduleid] ='PB01_RECIPEID' then [ppid] end )
	   else MAX(case when [moduleid] ='PB02_RECIPEID' and len([ppid])>0  then [ppid] else '-'  end ) end AS PB02_RECIPEID
  FROM [************].[BASEDATA].[dbo].[PPID_PARAMETER]
  where ([moduleid]='OLBSI_RECIPEID' or [moduleid]='SI_RECIPEID' or [moduleid]='PB02_RECIPEID' or [moduleid]='PB01_RECIPEID' or [moduleid]='SS_RECIPEID') and len([eqpid])>0
  group by [eqpid],[model]) AS m
    left join 
  (SELECT distinct [model],[ppid] AS  CPPPID FROM [************].[BASEDATA].[dbo].[PPID_PARAMETER]
  where [moduleid]='CPPPID')  AS o
  on  m.[PRODUCT]=o.[model]







-- step ppid_line
delete from  [product].[dbo].[ppid_line]
insert into [product].[dbo].[ppid_line]

select a.*,ISNULL([OLBSI_RECIPEID],'-') AS OLBSI_RECIPEID
      ,ISNULL([SI_RECIPEID],'-') AS SI_RECIPEID
      ,ISNULL([SS_RECIPEID],'-') AS SS_RECIPEID
      ,ISNULL([PB01_RECIPEID],'-') AS PB01_RECIPEID
      ,ISNULL([PB02_RECIPEID],'-') AS PB02_RECIPEID
      ,ISNULL([CP_RECIPEID],'-') AS CP_RECIPEID
	  ,ISNULL(case when b.[MOD]='1' then right([SI_RECIPEID],2) else substring([SI_RECIPEID],9,1) end,'')  as SI
	  ,c.pcbs,c.ag
from 
(SELECT [EQPID]
      ,[PRODUCT]
      ,SUM([OUT]) AS OUT
      ,MAX([DATE]) AS MAX_DATE
  FROM [Out_new].[dbo].[OUT_HH]
  WHERE [OUT]>0 AND  SUBSTRING([EQPID],3,2)='OL'
  GROUP BY [EQPID],[PRODUCT]) AS a
  LEFT JOIN [product].[dbo].[ppid] AS b
  ON a.[PRODUCT]=b.[PRODUCT] and left(a.[EQPID],1)=b.[MOD]
  left JOIN 
  (SELECT  case when [PROCESSID]='MODPROC' then'1'
            when  [PROCESSID]='MODPROC2' then'2'
			when  [PROCESSID]='MODPROC3' then'3' end as MOD
		,[PRODUCTID],
         COUNT(DISTINCT  CASE WHEN left([MATKIND],3)='PCB' THEN [MATKIND] END) AS pcbs
  		,COUNT (DISTINCT  CASE  WHEN MATKIND='PASTE' THEN [MATKIND] END )  as ag
		FROM [product].[dbo].[bom]
		where  (left([MATKIND],3)='PCB' OR MATKIND='PASTE')
		GROUP BY [PROCESSID],[PRODUCTID]) AS c
 ON a.[PRODUCT]=c.[PRODUCTID] and left(a.[EQPID],1)=c.[MOD]


delete from  [product].[dbo].[ppid_mod]
insert into [product].[dbo].[ppid_mod]

SELECT a.[MOD],a.[PRODUCT],ISNULL([OLBSI_RECIPEID],'-') AS OLBSI_RECIPEID
      ,ISNULL([SI_RECIPEID],'-') AS SI_RECIPEID
      ,ISNULL([SS_RECIPEID],'-') AS SS_RECIPEID
      ,ISNULL([PB01_RECIPEID],'-') AS PB01_RECIPEID
      ,ISNULL([PB02_RECIPEID],'-') AS PB02_RECIPEID
      ,ISNULL([CP_RECIPEID],'-') AS CP_RECIPEID
	  ,ISNULL(case when a.[MOD]='1' then right([SI_RECIPEID],2) else substring([SI_RECIPEID],9,1) end,'')  as SI
	  ,c.pcbs,c.ag
  FROM [product].[dbo].[ppid] as a
    left JOIN 
  (SELECT  case when [PROCESSID]='MODPROC' then'1'
            when  [PROCESSID]='MODPROC2' then'2'
			when  [PROCESSID]='MODPROC3' then'3' end as MOD
		,[PRODUCTID],
         COUNT(DISTINCT  CASE WHEN left([MATKIND],3)='PCB' THEN [MATKIND] END) AS pcbs
  		,COUNT (DISTINCT  CASE  WHEN MATKIND='PASTE' THEN [MATKIND] END )  as ag
		FROM [product].[dbo].[bom]
		where  (left([MATKIND],3)='PCB' OR MATKIND='PASTE')
		GROUP BY [PROCESSID],[PRODUCTID]) AS c
 ON a.[PRODUCT]=c.[PRODUCTID] and a.[MOD]=c.[MOD]





 -- step ppid_vs
 delete  FROM [product].[dbo].[ppid_vs]

insert into [product].[dbo].[ppid_vs]
    SELECT a.[MOD] AS MOD_A
      ,a.[PRODUCT] AS PRODUCT_A
	  ,a.FAB AS FAB_A
	  ,a.[JIG] as JIG_A
      ,a.[OLBSI_RECIPEID] AS OLBSI_RECIPEID_A
      ,a.[SI_RECIPEID] AS SI_RECIPEID_A
      ,a.[SS_RECIPEID] AS SS_RECIPEID_A
      ,a.[PB01_RECIPEID] AS PB01_RECIPEID_A
      ,a.[PB02_RECIPEID] AS PB02_RECIPEID_A
      ,a.[CP_RECIPEID] AS CP_RECIPEID_A
      ,a.[SI] AS SI_A
      ,a.[pcbs] AS pcbs_A
      ,a.[ag] AS ag_A
	  ,a.OLB_A
	  ,a.PB_A
	  ,a.PANEL_SIZE_X_A
	  ,a.PANEL_SIZE_Y_A
	  ,a.PANEL_XD_L_Mark_Pos_X_A
	  ,a.PANEL_XD_L_Mark_Pos_Y_A
	  ,a.PANEL_XU_L_Mark_Pos_X_A
	  ,a.PANEL_XU_L_Mark_Pos_Y_A
	  ,a.PANEL_XU_PITCH_1_A
	  ,a.PANEL_XU_PITCH_2_A
	  ,a.PANEL_XU_PITCH_3_A
	  ,a.PCBS_HEIGHT_A
	  ,a.PCBS_LENGTH_A
	  ,a.PCBS_MARK1_POS_X_A
	  ,a.PCBS_MARK1_POS_Y_A
	  ,a.PCBSR_HEIGHT_A
	  ,a.PCBSR_LENGTH_A
	  ,a.PCBSR_MARK1_POS_X_A
	  ,a.PCBSR_MARK1_POS_Y_A
	  ,b.EQPID
	  ,b.MAX_DATE
	  ,b.OUT
	  ,b.[MOD] AS MOD_B
      ,b.[PRODUCT] AS PRODUCT_B
	  ,b.FAB AS FAB_B
	  ,b.[JIG] as JIG_B
      ,b.[OLBSI_RECIPEID] AS OLBSI_RECIPEID_B
      ,b.[SI_RECIPEID] AS SI_RECIPEID_B
      ,b.[SS_RECIPEID] AS SS_RECIPEID_B
      ,b.[PB01_RECIPEID] AS PB01_RECIPEID_B
      ,b.[PB02_RECIPEID] AS PB02_RECIPEID_B
      ,b.[CP_RECIPEID] AS CP_RECIPEID_B
      ,b.[SI] AS SI_B
      ,b.[pcbs] AS pcbs_B
      ,b.[ag] AS ag_B
	  ,b.OLB_B
	  ,b.PB_B
	  ,b.PANEL_SIZE_X_B
	  ,b.PANEL_SIZE_Y_B
	  ,b.PANEL_XD_L_Mark_Pos_X_B
	  ,b.PANEL_XD_L_Mark_Pos_Y_B
	  ,b.PANEL_XU_L_Mark_Pos_X_B
	  ,b.PANEL_XU_L_Mark_Pos_Y_B
	  ,b.PANEL_XU_PITCH_1_B
	  ,b.PANEL_XU_PITCH_2_B
	  ,b.PANEL_XU_PITCH_3_B
	  ,b.PCBS_HEIGHT_B
	  ,b.PCBS_LENGTH_B
	  ,b.PCBS_MARK1_POS_X_B
	  ,b.PCBS_MARK1_POS_Y_B
	  ,b.PCBSR_HEIGHT_B
	  ,b.PCBSR_LENGTH_B
	  ,b.PCBSR_MARK1_POS_X_B
	  ,b.PCBSR_MARK1_POS_Y_B
	  ,case when a.[OLBSI_RECIPEID]=b.[OLBSI_RECIPEID] then 1 else 0 end as D_OLBSI_RECIPEID
	  ,case when a.[SI_RECIPEID]=b.[SI_RECIPEID] then 1 else 0 end D_SI_RECIPEID
	  ,case when a.[SS_RECIPEID]=b.[SS_RECIPEID] then 1 else 0 end D_SS_RECIPEID
	  ,case when a.[PB01_RECIPEID]=b.[PB01_RECIPEID]  and a.[PB02_RECIPEID]=b.[PB02_RECIPEID] then 1 else 0 end D_PB_RECIPEID
	  ,case when a.[CP_RECIPEID]=b.[CP_RECIPEID] then 1 else 0 end D_CP_RECIPEID
	  ,case when a.[OLBSI_RECIPEID]=b.[OLBSI_RECIPEID] then 1 else 0 end+case when a.[SI_RECIPEID]=b.[SI_RECIPEID] then 1 else 0 end
	  +case when a.[SS_RECIPEID]=b.[SS_RECIPEID] then 1 else 0 end 
	  +case when a.[PB01_RECIPEID]=b.[PB01_RECIPEID]  and a.[PB02_RECIPEID]=b.[PB02_RECIPEID] then 1 else 0 end AS SAME
	  ,case when a.[CP_RECIPEID]=b.[CP_RECIPEID] then 1 else 0 end AS SAME_C
	  ,case when substring(a.[PRODUCT],4,8)=substring(b.[PRODUCT],4,8) then 1 when substring(a.[PRODUCT],4,7)=substring(b.[PRODUCT],4,7) THEN 2  when substring(a.[PRODUCT],4,5)=substring(b.[PRODUCT],4,5) THEN 3  when substring(a.[PRODUCT],4,3)=substring(b.[PRODUCT],4,3) THEN 4 ELSE 88  end as ss
      ,case when a.PANEL_SIZE_X_A=b.PANEL_SIZE_X_B and  a.PANEL_SIZE_Y_A=b.PANEL_SIZE_Y_B  then 1 else 0 end as D_PANEL_SIZE
	  ,case when a.PANEL_XD_L_Mark_Pos_X_A=b.PANEL_XD_L_Mark_Pos_X_B  and a.PANEL_XD_L_Mark_Pos_Y_A=b.PANEL_XD_L_Mark_Pos_Y_B then 1 else 0 end as D_PANEL_XD_L_Mark_Pos
	  ,case when a.PANEL_XU_L_Mark_Pos_X_A=b.PANEL_XU_L_Mark_Pos_X_B and a.PANEL_XU_L_Mark_Pos_Y_A=b.PANEL_XU_L_Mark_Pos_Y_B then 1 else 0 end as D_PANEL_XU_L_Mark_Pos
	  ,case when a.PANEL_XU_PITCH_1_A=b.PANEL_XU_PITCH_1_B and a.PANEL_XU_PITCH_2_A=b.PANEL_XU_PITCH_2_B  and a.PANEL_XU_PITCH_3_A=b.PANEL_XU_PITCH_3_B then 1 else 0 end as D_PANEL_XU_PITCH
	  ,case when a.PCBS_HEIGHT_A=b.PCBS_HEIGHT_B   then 1 else 0 end as D_PCBSL_HEIGHT
      ,case when a.PCBS_LENGTH_A=b.PCBS_LENGTH_B  and a.PCBSR_HEIGHT_A=b.PCBSR_HEIGHT_B  then 1 else 0 end as D_PCBSR_HEIGHT
      ,case when a.PCBS_LENGTH_A=b.PCBS_LENGTH_B   then 1 else 0 end as D_PCBSL_LENGTH
      ,case when a.PCBS_LENGTH_A=b.PCBS_LENGTH_B  and a.PCBSR_LENGTH_A=b.PCBSR_LENGTH_B  then 1 else 0 end as D_PCBSR_LENGTH
	  ,case when a.PCBS_MARK1_POS_X_A=b.PCBS_MARK1_POS_X_B   then 1 else 0 end as D_PCBSL_MARK1_POS_XD
      ,case when a.PCBS_MARK1_POS_X_A=b.PCBS_MARK1_POS_X_B  and a.PCBSR_MARK1_POS_X_A=b.PCBSR_MARK1_POS_X_B  then 1 else 0 end as D_PCBSR_MARK1_POS_XD
      ,case when a.PCBS_MARK1_POS_Y_A=b.PCBS_MARK1_POS_Y_B   then 1 else 0 end as D_PCBSL_MARK1_POS_YD
      ,case when a.PCBS_MARK1_POS_Y_A=b.PCBS_MARK1_POS_Y_B  and  a.PCBSR_MARK1_POS_Y_A=b.PCBSR_MARK1_POS_Y_B  then 1 else 0 end as D_PCBSR_MARK1_POS_YD
   FROM 
   (SELECT m.*,n.OLB as OLB_A,n.PB as PB_A
	,isnull(o.PANEL_SIZE_X,'-') as PANEL_SIZE_X_A
	,isnull(o.PANEL_SIZE_Y,'-') as PANEL_SIZE_Y_A
	,isnull(o.PANEL_XD_L_Mark_Pos_X,'-') as PANEL_XD_L_Mark_Pos_X_A
	,isnull(o.PANEL_XD_L_Mark_Pos_Y,'-') as PANEL_XD_L_Mark_Pos_Y_A
	,isnull(o.PANEL_XU_L_Mark_Pos_X,'-') as PANEL_XU_L_Mark_Pos_X_A
	,isnull(o.PANEL_XU_L_Mark_Pos_Y,'-') as PANEL_XU_L_Mark_Pos_Y_A
	,isnull(o.PANEL_XU_PITCH_1,'-') as PANEL_XU_PITCH_1_A
	,isnull(o.PANEL_XU_PITCH_2,'-') as PANEL_XU_PITCH_2_A
	,isnull(o.PANEL_XU_PITCH_3,'-') as PANEL_XU_PITCH_3_A
	,isnull(p.PCBS_HEIGHT,'-') as PCBS_HEIGHT_A
	,isnull(p.PCBS_LENGTH,'-') as PCBS_LENGTH_A
	,isnull(p.PCBS_MARK1_POS_X,'-') as PCBS_MARK1_POS_X_A
	,isnull(p.PCBS_MARK1_POS_Y,'-') as PCBS_MARK1_POS_Y_A
	,isnull(q.PCBS_HEIGHT,'-') as PCBSR_HEIGHT_A
	,isnull(q.PCBS_LENGTH,'-') as PCBSR_LENGTH_A
	,isnull(q.PCBS_MARK1_POS_X,'-') as PCBSR_MARK1_POS_X_A
	,isnull(q.PCBS_MARK1_POS_Y,'-') as PCBSR_MARK1_POS_Y_A
  FROM (SELECT [MOD]
      ,aa.[PRODUCT]
	  ,[FAB]
	  ,[JIG]
      ,[OLBSI_RECIPEID]
      ,[SI_RECIPEID]
      ,[SS_RECIPEID]
      ,[PB01_RECIPEID]
      ,[PB02_RECIPEID]
      ,[CP_RECIPEID]
      ,[SI]
      ,[pcbs]
      ,[ag]
  FROM [product].[dbo].[ppid_mod] as aa
  left join 
  (SELECT distinct [PRODUCTID]
	  ,case when CHARINDEX('(',[PLANT])=0 then [PLANT] else 
	  left([PLANT],CHARINDEX('(',[PLANT])-1) end as FAB
  FROM [************].[BASEDATA].[dbo].[FIXPLAN]) as bb
  on aa.[PRODUCT]=bb.[PRODUCTID]
  left join [product].[dbo].[jig] as cc
  on aa.[PRODUCT]=cc.[PRODUCT]
  ) AS m
	left join
	(SELECT [PRODUCTID]
		,MAX(case when b.Remark='OLB' THEN b.Size END) AS OLB
		,MAX(case when b.Remark='PB' THEN b.Size END) AS PB
		FROM [product].[dbo].[bom] as a
		left join [product].[dbo].[ACF_SIZE] as b
		on a.MATCODE=b.CodeID
		where [MATKIND]='A_ACF'
		GROUP BY [PRODUCTID]) as n
	on m.[PRODUCT]=n.[PRODUCTID]
	left join [product].[dbo].[panel_size] as o
	on m.[PRODUCT]=o.[MATCODE]
	left join 
	(select * from [product].[dbo].[pcb_size] where [MATKIND]='PCBS' OR [MATKIND]='PCBSL' ) as p
	on m.[PRODUCT]=p.[PRODUCT]
	left join 
	(select * from [product].[dbo].[pcb_size] where [MATKIND]='PCBSR' ) as q
	on m.[PRODUCT]=q.[PRODUCT]) as a 
   CROSS JOIN
     (SELECT left(m.[EQPID],1) as MOD,m.*,n.OLB AS OLB_B,n.PB AS PB_B
	,isnull(o.PANEL_SIZE_X,'-') as PANEL_SIZE_X_B
	,isnull(o.PANEL_SIZE_Y,'-') as PANEL_SIZE_Y_B
	,isnull(o.PANEL_XD_L_Mark_Pos_X,'-') as PANEL_XD_L_Mark_Pos_X_B
	,isnull(o.PANEL_XD_L_Mark_Pos_Y,'-') as PANEL_XD_L_Mark_Pos_Y_B
	,isnull(o.PANEL_XU_L_Mark_Pos_X,'-') as PANEL_XU_L_Mark_Pos_X_B
	,isnull(o.PANEL_XU_L_Mark_Pos_Y,'-') as PANEL_XU_L_Mark_Pos_Y_B
	,isnull(o.PANEL_XU_PITCH_1,'-') as PANEL_XU_PITCH_1_B
	,isnull(o.PANEL_XU_PITCH_2,'-') as PANEL_XU_PITCH_2_B
	,isnull(o.PANEL_XU_PITCH_3,'-') as PANEL_XU_PITCH_3_B
	,isnull(p.PCBS_HEIGHT,'-') as PCBS_HEIGHT_B
	,isnull(p.PCBS_LENGTH,'-') as PCBS_LENGTH_B
	,isnull(p.PCBS_MARK1_POS_X,'-') as PCBS_MARK1_POS_X_B
	,isnull(p.PCBS_MARK1_POS_Y,'-') as PCBS_MARK1_POS_Y_B
	,isnull(q.PCBS_HEIGHT,'-') as PCBSR_HEIGHT_B
	,isnull(q.PCBS_LENGTH,'-') as PCBSR_LENGTH_B
	,isnull(q.PCBS_MARK1_POS_X,'-') as PCBSR_MARK1_POS_X_B
	,isnull(q.PCBS_MARK1_POS_Y,'-') as PCBSR_MARK1_POS_Y_B
  FROM (select r.*,OLBSI_RECIPEID,SI_RECIPEID,SS_RECIPEID,PB01_RECIPEID,PB02_RECIPEID,CP_RECIPEID,SI,pcbs,ag,JIG  from
		(select cc.*,dd.FAB from
 ( SELECT [EQPID]
      ,[PRODUCT]
      ,SUM([OUT]) AS OUT
      ,MAX([DATE]) AS MAX_DATE
  FROM [Out_new].[dbo].[OUT_HH]
  WHERE [OUT]>0 AND  SUBSTRING([EQPID],3,2)='OL'
  GROUP BY [EQPID],[PRODUCT]) as cc
    left join 
  (SELECT distinct [PRODUCTID]
	  ,case when CHARINDEX('(',[PLANT])=0 then [PLANT] else 
	  left([PLANT],CHARINDEX('(',[PLANT])-1) end as FAB
  FROM [************].[BASEDATA].[dbo].[FIXPLAN]) as dd
  on cc.[PRODUCT]=dd.[PRODUCTID]) as r
  left join [product].[dbo].[ppid_mod] as s
  on left(r.[EQPID],1)=s.MOD and r.PRODUCT=s.PRODUCT
    left join [product].[dbo].[jig] as t
  on r.[PRODUCT]=t.[PRODUCT]) AS m
	left join
	(SELECT [PRODUCTID]
		,MAX(case when b.Remark='OLB' THEN b.Size END) AS OLB
		,MAX(case when b.Remark='PB' THEN b.Size END) AS PB
		FROM [product].[dbo].[bom] as a
		left join [product].[dbo].[ACF_SIZE] as b
		on a.MATCODE=b.CodeID
		where [MATKIND]='A_ACF'
		GROUP BY [PRODUCTID]) as n
	on m.[PRODUCT]=n.[PRODUCTID]
	left join [product].[dbo].[panel_size] as o
	on m.[PRODUCT]=o.[MATCODE]
	left join 
	(select * from [product].[dbo].[pcb_size] where [MATKIND]='PCBS' OR [MATKIND]='PCBSL' ) as p
	on m.[PRODUCT]=p.[PRODUCT]
	left join 
	(select * from [product].[dbo].[pcb_size] where [MATKIND]='PCBSR' ) as q
	on m.[PRODUCT]=q.[PRODUCT]) as b
   WHERE a.[MOD]=b.[MOD] and left(a.[PRODUCT],1)!='L' and left(a.[PRODUCT],1)!='M' and left(b.[PRODUCT],1)!='L' and left(b.[PRODUCT],1)!='M'