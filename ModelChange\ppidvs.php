<?php
    header("Content-Type:application/json;charset=utf-8"); 
	include '../inc/sql.inc.php';
	include '../inc/config.inc.php';
	include '../inc/tool.inc.php';
	$serverName = "**************";
	$connectionInfo = array(  "UID"=>"sa", "PWD"=>"zz1t");
	$conn = sqlsrv_connect( $serverName, $connectionInfo );
	if( $conn === false ) {
	   die( print_r( sqlsrv_errors(), true));
	}
	
	$mod=$_POST["mod"];
	$product=$_POST["product"];
	$line=$_POST["line"];
	
	/*$mod='1';
	$product='12G3151D033002';
	$line='1SOL05';*/
	
	$a=substr($product,3,8);
	$b=substr($product,3,7);
	$c=substr($product,3,5);
	$d=substr($product,3,3);	
	
	$query="select top 1 [MOD_A]
		,[PRODUCT_A]
    	,[OLBSI_RECIPEID_A]
    	,[SI_RECIPEID_A]
    	,[SS_RECIPEID_A]
    	,[PB01_RECIPEID_A]
    	,[PB02_RECIPEID_A]
    	,[CP_RECIPEID_A]
	    ,[SI_A]
	    ,[pcbs_A]
	    ,[ag_A]
	    ,[OLB_A]
	    ,[PB_A]    	
		,isnull([PANEL_SIZE_X_A],'-') as PANEL_SIZE_X
	    ,isnull([PANEL_SIZE_Y_A],'-') as PANEL_SIZE_Y
	    ,isnull([PANEL_XD_L_Mark_Pos_X_A],'-') as PANEL_XD_L_Mark_Pos_X
	    ,isnull([PANEL_XD_L_Mark_Pos_Y_A],'-') as PANEL_XD_L_Mark_Pos_Y
	    ,isnull([PANEL_XU_L_Mark_Pos_X_A],'-') as PANEL_XU_L_Mark_Pos_X
	    ,isnull([PANEL_XU_L_Mark_Pos_Y_A],'-') as PANEL_XU_L_Mark_Pos_Y
	    ,isnull([PANEL_XU_PITCH_1_A],'-') as PANEL_XU_PITCH_1
	    ,isnull([PANEL_XU_PITCH_2_A],'-') as PANEL_XU_PITCH_2
	    ,isnull([PANEL_XU_PITCH_3_A],'-') as PANEL_XU_PITCH_3
	    ,isnull([PCBS_HEIGHT_A],'-') as PCBS_HEIGHT
	    ,isnull([PCBS_LENGTH_A],'-') as PCBS_LENGTH
	    ,isnull([PCBS_MARK1_POS_X_A],'-') as PCBS_MARK1_POS_X
	    ,isnull([PCBS_MARK1_POS_Y_A],'-') as PCBS_MARK1_POS_Y
	    ,isnull([PCBSR_HEIGHT_A],'-') as PCBSR_HEIGHT
	    ,isnull([PCBSR_LENGTH_A],'-') as PCBSR_LENGTH
	    ,isnull([PCBSR_MARK1_POS_X_A],'-') as PCBSR_MARK1_POS_X
	    ,isnull([PCBSR_MARK1_POS_Y_A],'-') as PCBSR_MARK1_POS_Y
	    ,[FAB_A]
	    ,[JIG_A]
	FROM [product].[dbo].[ppid_vs]
	WHERE [MOD_A] = '". $mod."' and  [PRODUCT_A] = '". $product."'
	 ";
	
	$result = sqlsrv_query($conn,$query);
	$date=sqlsrv_fetch_array($result);
	$PRODUCTs= $date[1];
	$OLBSI_RECIPEIDs= $date[2];
	$SI_RECIPEIDs= $date[3];
	$SS_RECIPEIDs= $date[4];
	$PB01_RECIPEIDs= $date[5];
	$PB02_RECIPEIDs= $date[6];
	$CP_RECIPEIDs= $date[7];
	$SIs= $date[8];
	$pbas= $date[9];
	if($date[10]=='0'){
		$ags= 'N';
	}else{
		$ags= 'Y';
	}
	//$ags= $date[10];
	$OLBS= $date[11];
	$PBS= $date[12];
	$PANEL_SIZE_XS= $date[13];
	$PANEL_SIZE_YS= $date[14];
	$PANEL_XD_L_Mark_Pos_XS= $date[15];
	$PANEL_XD_L_Mark_Pos_YS= $date[16];
	$PANEL_XU_L_Mark_Pos_XS= $date[17];
	$PANEL_XU_L_Mark_Pos_YS= $date[18];
	$PANEL_XU_PITCH_1S= $date[19];
	$PANEL_XU_PITCH_2S= $date[20];
	$PANEL_XU_PITCH_3S= $date[21];
	
	$PCBS_HEIGHTD= $date[22];
	$PCBS_LENGTHD= $date[23];
	$PCBS_MARK1_POS_XD= $date[24];
	$PCBS_MARK1_POS_YD= $date[25];	
	$PCBSR_HEIGHTD= $date[26];
	$PCBSR_LENGTHD= $date[27];
	$PCBSR_MARK1_POS_XD= $date[28];
	$PCBSR_MARK1_POS_YD= $date[29];		
	
	
	if($date[26]=='-'){
		$PCBS_HEIGHTS= $date[22];
		$PCBS_LENGTHS= $date[23];
		$PCBS_MARK1_POS_XS= $date[24];
		$PCBS_MARK1_POS_YS= $date[25];
	}else{
		$PCBS_HEIGHTS= '[L]'.$date[22].',[R]'.$date[26];
		$PCBS_LENGTHS= '[L]'.$date[23].',[R]'.$date[27];
		$PCBS_MARK1_POS_XS= '[L]'.$date[24].',[R]'.$date[28];
		$PCBS_MARK1_POS_YS= '[L]'.$date[25].',[R]'.$date[29];		
	}
	$outs= '';	
	$FAB_A= $date[30];	
	$JIG_A= $date[31];	
	
	$EQPID_d=[];
	$PRODUCT_d=[];
	$OUT_d=[];
	$MAX_DATE_d=[];
	$OLBSI_RECIPEID_d=[];
	$SI_RECIPEID_d=[];
	$SS_RECIPEID_d=[];
	$PB01_RECIPEID_d=[];
	$PB02_RECIPEID_d=[];
	$CP_RECIPEID_d=[];
	$SI_d=[];
	$PCBS_d=[];
	$AG_d=[];
	$OLBSI_RECIPEID_n=[];
	$SI_RECIPEID_n=[];
	$SS_RECIPEID_n=[];
	$PB_RECIPEID_n=[];
	$CP_RECIPEID_n=[];
	$SAME=[];
	$OLB=[];
	$PB=[];
	$PANEL_SIZE_X=[];
	$PANEL_SIZE_Y=[];
	$PANEL_XD_L_Mark_Pos_X=[];
	$PANEL_XD_L_Mark_Pos_Y=[];
	$PANEL_XU_L_Mark_Pos_X=[];
	$PANEL_XU_L_Mark_Pos_Y=[];
	$PANEL_XU_PITCH_1=[];
	$PANEL_XU_PITCH_2=[];
	$PANEL_XU_PITCH_3=[];
	$PCBS_HEIGHT=[];
	$PCBS_LENGTH=[];
	$PCBS_MARK1_POS_X=[];
	$PCBS_MARK1_POS_Y=[];
	$PCBSR_HEIGHT=[];
	$PCBSR_LENGTH=[];
	$PCBSR_MARK1_POS_X=[];
	$PCBSR_MARK1_POS_Y=[];
	$D_PANEL_SIZE=[];
	$D_PANEL_XD_L_Mark_Pos=[];
	$D_PANEL_XU_L_Mark_Pos=[];
	$D_PANEL_XU_PITCH=[];
	$D_PCBS_HEIGHT=[];
	$D_PCBS_LENGTH=[];
	$D_PCBSR_MARK1_POS_XD=[];
	$D_PCBSR_MARK1_POS_YD=[];
	$FAB_B=[];
	$JIG_B=[];

$query2="SELECT TOP 5 [EQPID]
    ,[PRODUCT_B] as PRODUCT
    ,[OUT]
    ,[MAX_DATE]
    ,[OLBSI_RECIPEID_B] as OLBSI_RECIPEID
    ,[SI_RECIPEID_B] as SI_RECIPEID
    ,[SS_RECIPEID_B] as SS_RECIPEID
    ,[PB01_RECIPEID_B] as PB01_RECIPEID
    ,[PB02_RECIPEID_B] as PB02_RECIPEID
    ,[CP_RECIPEID_B] as CP_RECIPEID
    ,[SI_B] as SI
    ,[pcbs_B] as PCBS
    ,[ag_B] as AG
	,[D_OLBSI_RECIPEID] as D_OLBSI_RECIPEID
	,[D_SI_RECIPEID] as D_SI_RECIPEID
	,[D_SS_RECIPEID] as D_SS_RECIPEID
	,[D_PB_RECIPEID] as D_PB_RECIPEID
	,[D_CP_RECIPEID] as D_CP_RECIPEID
	,[SAME]
	,[ss]
	,[OLB_B] as OLB
	,[PB_B] as PB
	,[PANEL_SIZE_X_B] as PANEL_SIZE_X
    ,[PANEL_SIZE_Y_B] as PANEL_SIZE_Y
    ,[D_PANEL_SIZE]
    ,[PANEL_XD_L_Mark_Pos_X_B] as PANEL_XD_L_Mark_Pos_X
    ,[PANEL_XD_L_Mark_Pos_Y_B] as PANEL_XD_L_Mark_Pos_Y
    ,[D_PANEL_XD_L_Mark_Pos]
    ,[PANEL_XU_L_Mark_Pos_X_B] as PANEL_XU_L_Mark_Pos_X
    ,[PANEL_XU_L_Mark_Pos_Y_B] as PANEL_XU_L_Mark_Pos_Y
    ,[D_PANEL_XU_L_Mark_Pos]
    ,[PANEL_XU_PITCH_1_B] as PANEL_XU_PITCH_1
    ,[PANEL_XU_PITCH_2_B] as PANEL_XU_PITCH_2
    ,[PANEL_XU_PITCH_3_B] as PANEL_XU_PITCH_3
    ,[D_PANEL_XU_PITCH]
    ,[PCBS_HEIGHT_B] as PCBS_HEIGHT
    ,[PCBS_LENGTH_B] as PCBS_LENGTH
    ,[PCBS_MARK1_POS_X_B] as PCBS_MARK1_POS_X
    ,[PCBS_MARK1_POS_Y_B] as PCBS_MARK1_POS_Y
    ,[PCBSR_HEIGHT_B] as PCBSR_HEIGHT
    ,[PCBSR_LENGTH_B] as PCBSR_LENGTH
    ,D_PCBSL_HEIGHT
    ,D_PCBSR_HEIGHT
    ,D_PCBSL_LENGTH
    ,D_PCBSR_LENGTH
    ,[PCBSR_MARK1_POS_X_B] as PCBSR_MARK1_POS_X
    ,[PCBSR_MARK1_POS_Y_B] as PCBSR_MARK1_POS_Y
    ,D_PCBSL_MARK1_POS_XD
    ,D_PCBSR_MARK1_POS_XD
    ,D_PCBSL_MARK1_POS_YD
    ,D_PCBSR_MARK1_POS_YD
    ,FAB_B
    ,JIG_B
	FROM [product].[dbo].[ppid_vs]
	WHERE [EQPID] =  '". $line."' and [PRODUCT_A] = '". $product."'
	ORDER BY [SAME] DESC,[ss],[D_CP_RECIPEID] desc,[MAX_DATE] desc,[PRODUCT_B]";
	
	$result2 = sqlsrv_query($conn, $query2);
	while($row2 = sqlsrv_fetch_array($result2))
	 {$rows[]=$row2;}
	$num=count($rows); 
	for($i=0;$i<$num;$i++){
		array_push($EQPID_d,$rows[$i]['EQPID']);
		array_push($PRODUCT_d,$rows[$i]['PRODUCT']);
		array_push($OUT_d,$rows[$i]['OUT']);
		array_push($MAX_DATE_d,$rows[$i]['MAX_DATE']);
		array_push($OLBSI_RECIPEID_d,$rows[$i]['OLBSI_RECIPEID']);
		array_push($SI_RECIPEID_d,$rows[$i]['SI_RECIPEID']);
		array_push($SS_RECIPEID_d,$rows[$i]['SS_RECIPEID']);
		array_push($PB01_RECIPEID_d,$rows[$i]['PB01_RECIPEID']);
		array_push($PB02_RECIPEID_d,$rows[$i]['PB02_RECIPEID']);
		array_push($CP_RECIPEID_d,$rows[$i]['CP_RECIPEID']);
		array_push($SI_d,$rows[$i]['SI']);
		array_push($PCBS_d,$rows[$i]['PCBS']);
		if($rows[$i]['AG']=='0'){
			array_push($AG_d,'N');
		}else{
			array_push($AG_d,'Y');
		}
		//array_push($AG_d,$rows[$i]['AG']);
		array_push($OLBSI_RECIPEID_n,$rows[$i]['D_OLBSI_RECIPEID']);
		array_push($SI_RECIPEID_n,$rows[$i]['D_SI_RECIPEID']);
		array_push($SS_RECIPEID_n,$rows[$i]['D_SS_RECIPEID']);
		array_push($PB_RECIPEID_n,$rows[$i]['D_PB_RECIPEID']);
		array_push($CP_RECIPEID_n,$rows[$i]['D_CP_RECIPEID']);
		array_push($SAME,$rows[$i]['SAME']);
		array_push($OLB,$rows[$i]['OLB']);
		array_push($PB,$rows[$i]['PB']);
		array_push($PANEL_SIZE_X,$rows[$i]['PANEL_SIZE_X']);
		array_push($PANEL_SIZE_Y,$rows[$i]['PANEL_SIZE_Y']);
		array_push($PANEL_XD_L_Mark_Pos_X,$rows[$i]['PANEL_XD_L_Mark_Pos_X']);
		array_push($PANEL_XD_L_Mark_Pos_Y,$rows[$i]['PANEL_XD_L_Mark_Pos_Y']);
		array_push($PANEL_XU_L_Mark_Pos_X,$rows[$i]['PANEL_XU_L_Mark_Pos_X']);
		array_push($PANEL_XU_L_Mark_Pos_Y,$rows[$i]['PANEL_XU_L_Mark_Pos_Y']);
		array_push($PANEL_XU_PITCH_1,$rows[$i]['PANEL_XU_PITCH_1']);
		array_push($PANEL_XU_PITCH_2,$rows[$i]['PANEL_XU_PITCH_2']);
		array_push($PANEL_XU_PITCH_3,$rows[$i]['PANEL_XU_PITCH_3']);
		
		if($rows[$i]['PCBSR_HEIGHT']=='-'){
			array_push($PCBS_HEIGHT,$rows[$i]['PCBS_HEIGHT']);
			array_push($PCBS_LENGTH,$rows[$i]['PCBS_LENGTH']);
			array_push($PCBS_MARK1_POS_X,$rows[$i]['PCBS_MARK1_POS_X']);
			array_push($PCBS_MARK1_POS_Y,$rows[$i]['PCBS_MARK1_POS_Y']);			
		}else{
			array_push($PCBS_HEIGHT,'[L]'.$rows[$i]['PCBS_HEIGHT'].',[R]'.$rows[$i]['PCBSR_HEIGHT']);
			array_push($PCBS_LENGTH,'[L]'.$rows[$i]['PCBS_LENGTH'].',[R]'.$rows[$i]['PCBSR_LENGTH']);
			array_push($PCBS_MARK1_POS_X,'[L]'.$rows[$i]['PCBS_MARK1_POS_X'].',[R]'.$rows[$i]['PCBSR_MARK1_POS_X']);
			array_push($PCBS_MARK1_POS_Y,'[L]'.$rows[$i]['PCBS_MARK1_POS_Y'].',[R]'.$rows[$i]['PCBSR_MARK1_POS_Y']);			
		}
		array_push($D_PANEL_SIZE,$rows[$i]['D_PANEL_SIZE']);
		array_push($D_PANEL_XD_L_Mark_Pos,$rows[$i]['D_PANEL_XD_L_Mark_Pos']);
		array_push($D_PANEL_XU_L_Mark_Pos,$rows[$i]['D_PANEL_XU_L_Mark_Pos']);
		array_push($D_PANEL_XU_PITCH,$rows[$i]['D_PANEL_XU_PITCH']);
		
		if($rows[$i]['PCBS']=='1'){
			array_push($D_PCBS_HEIGHT,$rows[$i]['D_PCBSL_HEIGHT']);
			array_push($D_PCBS_LENGTH,$rows[$i]['D_PCBSL_LENGTH']);
			array_push($D_PCBSR_MARK1_POS_XD,$rows[$i]['D_PCBSL_MARK1_POS_XD']);
			array_push($D_PCBSR_MARK1_POS_YD,$rows[$i]['D_PCBSL_MARK1_POS_YD']);			
		}else{
			array_push($D_PCBS_HEIGHT,$rows[$i]['D_PCBSR_HEIGHT']);
			array_push($D_PCBS_LENGTH,$rows[$i]['D_PCBSR_LENGTH']);
			array_push($D_PCBSR_MARK1_POS_XD,$rows[$i]['D_PCBSR_MARK1_POS_XD']);
			array_push($D_PCBSR_MARK1_POS_YD,$rows[$i]['D_PCBSR_MARK1_POS_YD']);				
		}
		array_push($FAB_B,$rows[$i]['FAB_B']);
		array_push($JIG_B,$rows[$i]['JIG_B']);
	}		
		
	
	
	$query2="SELECT [EQPID],max([DATE])
		FROM [Out_new].[dbo].[OUT_HH] WHERE [EQPID] = '". $line."' and  [PRODUCT] = '". $product."' and [OUT]>0 
		group by [EQPID],[PRODUCT]";
	
	$result2 = sqlsrv_query($conn,$query2);
	$date2=sqlsrv_fetch_array($result2);
	if(strlen($date2[1])==0){
		$datea='-';
	}else{
		$datea=$date2[1];
	}
	


	/*$query3="  SELECT  COUNT(DISTINCT  CASE WHEN left([MATKIND],3)='PCB' THEN [MATKIND] END) AS pcbs
  		,COUNT (DISTINCT  CASE  WHEN MATKIND='PASTE' THEN [MATKIND] END )  as ag
		FROM [product].[dbo].[bom]
		where [PRODUCTID]='".$product."' and (left([MATKIND],3)='PCB' OR MATKIND='PASTE')";
		$result3 = sqlsrv_query($conn,$query3);
		$date3=sqlsrv_fetch_array($result3);	
	$pbas=$date3[0];
	$ags=$date3[1];*/
	

	
	
	$str='{"pbas":'.json_encode($pbas).'
		,"ags":'.json_encode($ags).'
		,"outs":'.json_encode($outs).'
		,"datea":'.json_encode($datea).'
		,"PRODUCTs":'.json_encode($PRODUCTs).'
		,"FAB_A":'.json_encode($FAB_A).'
		,"JIG_A":'.json_encode($JIG_A).'
		,"OLBSI_RECIPEIDs":'.json_encode($OLBSI_RECIPEIDs).'
		,"SI_RECIPEIDs":'.json_encode($SI_RECIPEIDs).'
		,"SS_RECIPEIDs":'.json_encode($SS_RECIPEIDs).'
		,"PB01_RECIPEIDs":'.json_encode($PB01_RECIPEIDs).'
		,"PB02_RECIPEIDs":'.json_encode($PB02_RECIPEIDs).'
		,"CP_RECIPEIDs":'.json_encode($CP_RECIPEIDs).'
		,"SIs":'.json_encode($SIs).'
		,"EQPID_d":'.json_encode($EQPID_d).'
		,"PRODUCT_d":'.json_encode($PRODUCT_d).'
		,"FAB_B":'.json_encode($FAB_B).'
		,"JIG_B":'.json_encode($JIG_B).'
		,"OUT_d":'.json_encode($OUT_d).'
		,"MAX_DATE_d":'.json_encode($MAX_DATE_d).'
		,"OLBSI_RECIPEID_d":'.json_encode($OLBSI_RECIPEID_d).'
		,"SI_RECIPEID_d":'.json_encode($SI_RECIPEID_d).'
		,"SS_RECIPEID_d":'.json_encode($SS_RECIPEID_d).'
		,"PB01_RECIPEID_d":'.json_encode($PB01_RECIPEID_d).'
		,"PB02_RECIPEID_d":'.json_encode($PB02_RECIPEID_d).'
		,"CP_RECIPEID_d":'.json_encode($CP_RECIPEID_d).'
		,"SI_d":'.json_encode($SI_d).'
		,"PCBS_d":'.json_encode($PCBS_d).'
		,"AG_d":'.json_encode($AG_d).'
		,"OLBSI_RECIPEID_n":'.json_encode($OLBSI_RECIPEID_n).'
		,"SI_RECIPEID_n":'.json_encode($SI_RECIPEID_n).'
		,"SS_RECIPEID_n":'.json_encode($SS_RECIPEID_n).'
		,"PB_RECIPEID_n":'.json_encode($PB_RECIPEID_n).'
		,"CP_RECIPEID_n":'.json_encode($CP_RECIPEID_n).'
		,"SAME":'.json_encode($SAME).'
		,"OLB":'.json_encode($OLB).'
		,"PB":'.json_encode($PB).'
		,"OLBS":'.json_encode($OLBS).'
		,"PBS":'.json_encode($PBS).'
		,"PANEL_SIZE_X":'.json_encode($PANEL_SIZE_X).'
		,"PANEL_SIZE_Y":'.json_encode($PANEL_SIZE_Y).'
		,"PANEL_XD_L_Mark_Pos_X":'.json_encode($PANEL_XD_L_Mark_Pos_X).'
		,"PANEL_XD_L_Mark_Pos_Y":'.json_encode($PANEL_XD_L_Mark_Pos_Y).'
		,"PANEL_XU_L_Mark_Pos_X":'.json_encode($PANEL_XU_L_Mark_Pos_X).'
		,"PANEL_XU_L_Mark_Pos_Y":'.json_encode($PANEL_XU_L_Mark_Pos_Y).'
		,"PANEL_XU_PITCH_1":'.json_encode($PANEL_XU_PITCH_1).'
		,"PANEL_XU_PITCH_2":'.json_encode($PANEL_XU_PITCH_2).'
		,"PANEL_XU_PITCH_3":'.json_encode($PANEL_XU_PITCH_3).'
		,"PCBS_HEIGHT":'.json_encode($PCBS_HEIGHT).'
		,"PCBS_LENGTH":'.json_encode($PCBS_LENGTH).'
		,"PCBS_MARK1_POS_X":'.json_encode($PCBS_MARK1_POS_X).'
		,"PCBS_MARK1_POS_Y":'.json_encode($PCBS_MARK1_POS_Y).'
		,"PANEL_SIZE_XS":'.json_encode($PANEL_SIZE_XS).'
		,"PANEL_SIZE_YS":'.json_encode($PANEL_SIZE_YS).'
		,"PANEL_XD_L_Mark_Pos_XS":'.json_encode($PANEL_XD_L_Mark_Pos_XS).'
		,"PANEL_XD_L_Mark_Pos_YS":'.json_encode($PANEL_XD_L_Mark_Pos_YS).'
		,"PANEL_XU_L_Mark_Pos_XS":'.json_encode($PANEL_XU_L_Mark_Pos_XS).'
		,"PANEL_XU_L_Mark_Pos_YS":'.json_encode($PANEL_XU_L_Mark_Pos_YS).'
		,"PANEL_XU_PITCH_1S":'.json_encode($PANEL_XU_PITCH_1S).'
		,"PANEL_XU_PITCH_2S":'.json_encode($PANEL_XU_PITCH_2S).'
		,"PANEL_XU_PITCH_3S":'.json_encode($PANEL_XU_PITCH_3S).'
		,"PCBS_HEIGHTS":'.json_encode($PCBS_HEIGHTS).'
		,"PCBS_LENGTHS":'.json_encode($PCBS_LENGTHS).'
		,"PCBS_MARK1_POS_XS":'.json_encode($PCBS_MARK1_POS_XS).'
		,"PCBS_MARK1_POS_YS":'.json_encode($PCBS_MARK1_POS_YS).'
		,"D_PANEL_SIZE":'.json_encode($D_PANEL_SIZE).'
		,"D_PANEL_XD_L_Mark_Pos":'.json_encode($D_PANEL_XD_L_Mark_Pos).'
		,"D_PANEL_XU_L_Mark_Pos":'.json_encode($D_PANEL_XU_L_Mark_Pos).'
		,"D_PANEL_XU_PITCH":'.json_encode($D_PANEL_XU_PITCH).'
		,"D_PCBS_HEIGHT":'.json_encode($D_PCBS_HEIGHT).'
		,"D_PCBS_LENGTH":'.json_encode($D_PCBS_LENGTH).'
		,"D_PCBSR_MARK1_POS_XD":'.json_encode($D_PCBSR_MARK1_POS_XD).'
		,"D_PCBSR_MARK1_POS_YD":'.json_encode($D_PCBSR_MARK1_POS_YD).'
	}';
	echo $str;		


?>