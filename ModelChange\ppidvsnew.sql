-- step ppid
DELETE FROM [product].[dbo].[ppid]
INSERT INTO [product].[dbo].[ppid]
SELECT m.*, o.CPPPID as CP_RECIPEID 
FROM (
    SELECT DISTINCT 
        LEFT([eqpid],1) AS MOD,
        [model] AS PRODUCT,
        MAX(CASE WHEN [moduleid] = 'OLBSI_RECIPEID' THEN [ppid] END) AS OLBSI_RECIPEID,
        MAX(CASE WHEN [moduleid] = 'SI_RECIPEID' THEN [ppid] END) AS SI_RECIPEID,
        MAX(CASE WHEN [moduleid] = 'SS_RECIPEID' THEN [ppid] END) AS SS_RECIPEID,
        -- 优化PB01_RECIPEID逻辑
        CASE 
            WHEN MAX(CASE WHEN [moduleid] = 'PB01_RECIPEID' AND LEN([ppid]) > 0 THEN [ppid] ELSE '-' END) = '-' THEN 
                MAX(CASE WHEN [moduleid] = 'PB02_RECIPEID' AND <PERSON>EN([ppid]) > 0 THEN [ppid] ELSE '-' END)
            WHEN SUBSTRING(MAX(CASE WHEN [moduleid] = 'PB01_RECIPEID' AND LEN([ppid]) > 0 THEN [ppid] ELSE '-' END), 13, 2) = 'XR' THEN 
                MAX(CASE WHEN [moduleid] = 'PB02_RECIPEID' THEN [ppid] END)
            ELSE 
                MAX(CASE WHEN [moduleid] = 'PB01_RECIPEID' AND LEN([ppid]) > 0 THEN [ppid] ELSE '-' END)
        END AS PB01_RECIPEID,
        -- 优化PB02_RECIPEID逻辑
        CASE 
            WHEN MAX(CASE WHEN [moduleid] = 'PB01_RECIPEID' AND LEN([ppid]) > 0 THEN [ppid] ELSE '-' END) = '-' THEN '-'
            WHEN MAX(CASE WHEN [moduleid] = 'PB01_RECIPEID' THEN [ppid] END) = MAX(CASE WHEN [moduleid] = 'PB02_RECIPEID' THEN [ppid] END) THEN '-'
            WHEN SUBSTRING(MAX(CASE WHEN [moduleid] = 'PB02_RECIPEID' AND LEN([ppid]) > 0 THEN [ppid] ELSE '-' END), 13, 2) IN ('XL', 'XC') THEN 
                MAX(CASE WHEN [moduleid] = 'PB01_RECIPEID' THEN [ppid] END)
            ELSE 
                MAX(CASE WHEN [moduleid] = 'PB02_RECIPEID' AND LEN([ppid]) > 0 THEN [ppid] ELSE '-' END)
        END AS PB02_RECIPEID
    FROM [************].[BASEDATA].[dbo].[PPID_PARAMETER]
    WHERE [moduleid] IN ('OLBSI_RECIPEID', 'SI_RECIPEID', 'PB02_RECIPEID', 'PB01_RECIPEID', 'SS_RECIPEID') 
        AND LEN([eqpid]) > 0
    GROUP BY [eqpid], [model]
) AS m
LEFT JOIN (
    SELECT DISTINCT [model], [ppid] AS CPPPID 
    FROM [************].[BASEDATA].[dbo].[PPID_PARAMETER]
    WHERE [moduleid] = 'CPPPID'
) AS o ON m.[PRODUCT] = o.[model]

-- step ppid_line
DELETE FROM [product].[dbo].[ppid_line]
INSERT INTO [product].[dbo].[ppid_line]
SELECT 
    a.*,
    ISNULL([OLBSI_RECIPEID], '-') AS OLBSI_RECIPEID,
    ISNULL([SI_RECIPEID], '-') AS SI_RECIPEID,
    ISNULL([SS_RECIPEID], '-') AS SS_RECIPEID,
    ISNULL([PB01_RECIPEID], '-') AS PB01_RECIPEID,
    ISNULL([PB02_RECIPEID], '-') AS PB02_RECIPEID,
    ISNULL([CP_RECIPEID], '-') AS CP_RECIPEID,
    ISNULL(CASE WHEN b.[MOD] = '1' THEN RIGHT([SI_RECIPEID], 2) ELSE SUBSTRING([SI_RECIPEID], 9, 1) END, '') AS SI,
    c.pcbs,
    c.ag
FROM (
    SELECT 
        [EQPID],
        [PRODUCT],
        SUM([OUT]) AS OUT,
        MAX([DATE]) AS MAX_DATE
    FROM [Out_new].[dbo].[OUT_HH]
    WHERE [OUT] > 0 AND SUBSTRING([EQPID], 3, 2) = 'OL'
    GROUP BY [EQPID], [PRODUCT]
) AS a
LEFT JOIN [product].[dbo].[ppid] AS b ON a.[PRODUCT] = b.[PRODUCT] AND LEFT(a.[EQPID], 1) = b.[MOD]
LEFT JOIN (
    SELECT  
        CASE 
            WHEN [PROCESSID] = 'MODPROC' THEN '1'
            WHEN [PROCESSID] = 'MODPROC2' THEN '2'
            WHEN [PROCESSID] = 'MODPROC3' THEN '3' 
        END AS MOD,
        [PRODUCTID],
        COUNT(DISTINCT CASE WHEN LEFT([MATKIND], 3) = 'PCB' THEN [MATKIND] END) AS pcbs,
        COUNT(DISTINCT CASE WHEN MATKIND = 'PASTE' THEN [MATKIND] END) AS ag
    FROM [product].[dbo].[bom]
    WHERE LEFT([MATKIND], 3) = 'PCB' OR MATKIND = 'PASTE'
    GROUP BY [PROCESSID], [PRODUCTID]
) AS c ON a.[PRODUCT] = c.[PRODUCTID] AND LEFT(a.[EQPID], 1) = c.[MOD]

-- step ppid_mod
DELETE FROM [product].[dbo].[ppid_mod]
INSERT INTO [product].[dbo].[ppid_mod]
SELECT 
    a.[MOD],
    a.[PRODUCT],
    ISNULL([OLBSI_RECIPEID], '-') AS OLBSI_RECIPEID,
    ISNULL([SI_RECIPEID], '-') AS SI_RECIPEID,
    ISNULL([SS_RECIPEID], '-') AS SS_RECIPEID,
    ISNULL([PB01_RECIPEID], '-') AS PB01_RECIPEID,
    ISNULL([PB02_RECIPEID], '-') AS PB02_RECIPEID,
    ISNULL([CP_RECIPEID], '-') AS CP_RECIPEID,
    ISNULL(CASE WHEN a.[MOD] = '1' THEN RIGHT([SI_RECIPEID], 2) ELSE SUBSTRING([SI_RECIPEID], 9, 1) END, '') AS SI,
    c.pcbs,
    c.ag
FROM [product].[dbo].[ppid] AS a
LEFT JOIN (
    SELECT  
        CASE 
            WHEN [PROCESSID] = 'MODPROC' THEN '1'
            WHEN [PROCESSID] = 'MODPROC2' THEN '2'
            WHEN [PROCESSID] = 'MODPROC3' THEN '3' 
        END AS MOD,
        [PRODUCTID],
        COUNT(DISTINCT CASE WHEN LEFT([MATKIND], 3) = 'PCB' THEN [MATKIND] END) AS pcbs,
        COUNT(DISTINCT CASE WHEN MATKIND = 'PASTE' THEN [MATKIND] END) AS ag
    FROM [product].[dbo].[bom]
    WHERE LEFT([MATKIND], 3) = 'PCB' OR MATKIND = 'PASTE'
    GROUP BY [PROCESSID], [PRODUCTID]
) AS c ON a.[PRODUCT] = c.[PRODUCTID] AND a.[MOD] = c.[MOD]

-- 定义CTE以减少重复查询
WITH bom_info AS (
    SELECT [PRODUCTID],
        MAX(CASE WHEN b.Remark = 'OLB' THEN b.Size END) AS OLB,
        MAX(CASE WHEN b.Remark = 'PB' THEN b.Size END) AS PB
    FROM [product].[dbo].[bom] AS a
    LEFT JOIN [product].[dbo].[ACF_SIZE] AS b ON a.MATCODE = b.CodeID
    WHERE [MATKIND] = 'A_ACF'
    GROUP BY [PRODUCTID]
),
panel_size_info AS (
    SELECT [MATCODE], [PRODUCT], [PANEL_SIZE_X], [PANEL_SIZE_Y], 
        [PANEL_XD_L_Mark_Pos_X], [PANEL_XD_L_Mark_Pos_Y],
        [PANEL_XU_L_Mark_Pos_X], [PANEL_XU_L_Mark_Pos_Y],
        [PANEL_XU_PITCH_1], [PANEL_XU_PITCH_2], [PANEL_XU_PITCH_3]
    FROM [product].[dbo].[panel_size]
),
pcb_size_pcbs AS (
    SELECT * FROM [product].[dbo].[pcb_size] 
    WHERE [MATKIND] IN ('PCBS', 'PCBSL')
),
pcb_size_pcbsr AS (
    SELECT * FROM [product].[dbo].[pcb_size] 
    WHERE [MATKIND] = 'PCBSR'
),
fixplan_info AS (
    SELECT DISTINCT [PRODUCTID],
        CASE 
            WHEN CHARINDEX('(', [PLANT]) = 0 THEN [PLANT] 
            ELSE LEFT([PLANT], CHARINDEX('(', [PLANT]) - 1) 
        END AS FAB
    FROM [************].[BASEDATA].[dbo].[FIXPLAN]
),
-- 定义产品A的数据集
product_a_data AS (
    SELECT m.*, n.OLB AS OLB_A, n.PB AS PB_A,
        ISNULL(o.PANEL_SIZE_X, '-') AS PANEL_SIZE_X_A,
        ISNULL(o.PANEL_SIZE_Y, '-') AS PANEL_SIZE_Y_A,
        ISNULL(o.PANEL_XD_L_Mark_Pos_X, '-') AS PANEL_XD_L_Mark_Pos_X_A,
        ISNULL(o.PANEL_XD_L_Mark_Pos_Y, '-') AS PANEL_XD_L_Mark_Pos_Y_A,
        ISNULL(o.PANEL_XU_L_Mark_Pos_X, '-') AS PANEL_XU_L_Mark_Pos_X_A,
        ISNULL(o.PANEL_XU_L_Mark_Pos_Y, '-') AS PANEL_XU_L_Mark_Pos_Y_A,
        ISNULL(o.PANEL_XU_PITCH_1, '-') AS PANEL_XU_PITCH_1_A,
        ISNULL(o.PANEL_XU_PITCH_2, '-') AS PANEL_XU_PITCH_2_A,
        ISNULL(o.PANEL_XU_PITCH_3, '-') AS PANEL_XU_PITCH_3_A,
        ISNULL(p.PCBS_HEIGHT, '-') AS PCBS_HEIGHT_A,
        ISNULL(p.PCBS_LENGTH, '-') AS PCBS_LENGTH_A,
        ISNULL(p.PCBS_MARK1_POS_X, '-') AS PCBS_MARK1_POS_X_A,
        ISNULL(p.PCBS_MARK1_POS_Y, '-') AS PCBS_MARK1_POS_Y_A,
        ISNULL(q.PCBS_HEIGHT, '-') AS PCBSR_HEIGHT_A,
        ISNULL(q.PCBS_LENGTH, '-') AS PCBSR_LENGTH_A,
        ISNULL(q.PCBS_MARK1_POS_X, '-') AS PCBSR_MARK1_POS_X_A,
        ISNULL(q.PCBS_MARK1_POS_Y, '-') AS PCBSR_MARK1_POS_Y_A
    FROM (
        SELECT [MOD], aa.[PRODUCT], [FAB], [JIG],
            [OLBSI_RECIPEID], [SI_RECIPEID], [SS_RECIPEID],
            [PB01_RECIPEID], [PB02_RECIPEID], [CP_RECIPEID],
            [SI], [pcbs], [ag]
        FROM [product].[dbo].[ppid_mod] AS aa
        LEFT JOIN fixplan_info AS bb ON aa.[PRODUCT] = bb.[PRODUCTID]
        LEFT JOIN [product].[dbo].[jig] AS cc ON aa.[PRODUCT] = cc.[PRODUCT]
    ) AS m
    LEFT JOIN bom_info AS n ON m.[PRODUCT] = n.[PRODUCTID]
    LEFT JOIN panel_size_info AS o ON m.[PRODUCT] = o.[MATCODE]
    LEFT JOIN pcb_size_pcbs AS p ON m.[PRODUCT] = p.[PRODUCT]
    LEFT JOIN pcb_size_pcbsr AS q ON m.[PRODUCT] = q.[PRODUCT]
),
-- 定义产品B的数据集
product_b_data AS (
    SELECT LEFT(m.[EQPID], 1) AS MOD, m.*, n.OLB AS OLB_B, n.PB AS PB_B,
        ISNULL(o.PANEL_SIZE_X, '-') AS PANEL_SIZE_X_B,
        ISNULL(o.PANEL_SIZE_Y, '-') AS PANEL_SIZE_Y_B,
        ISNULL(o.PANEL_XD_L_Mark_Pos_X, '-') AS PANEL_XD_L_Mark_Pos_X_B,
        ISNULL(o.PANEL_XD_L_Mark_Pos_Y, '-') AS PANEL_XD_L_Mark_Pos_Y_B,
        ISNULL(o.PANEL_XU_L_Mark_Pos_X, '-') AS PANEL_XU_L_Mark_Pos_X_B,
        ISNULL(o.PANEL_XU_L_Mark_Pos_Y, '-') AS PANEL_XU_L_Mark_Pos_Y_B,
        ISNULL(o.PANEL_XU_PITCH_1, '-') AS PANEL_XU_PITCH_1_B,
        ISNULL(o.PANEL_XU_PITCH_2, '-') AS PANEL_XU_PITCH_2_B,
        ISNULL(o.PANEL_XU_PITCH_3, '-') AS PANEL_XU_PITCH_3_B,
        ISNULL(p.PCBS_HEIGHT, '-') AS PCBS_HEIGHT_B,
        ISNULL(p.PCBS_LENGTH, '-') AS PCBS_LENGTH_B,
        ISNULL(p.PCBS_MARK1_POS_X, '-') AS PCBS_MARK1_POS_X_B,
        ISNULL(p.PCBS_MARK1_POS_Y, '-') AS PCBS_MARK1_POS_Y_B,
        ISNULL(q.PCBS_HEIGHT, '-') AS PCBSR_HEIGHT_B,
        ISNULL(q.PCBS_LENGTH, '-') AS PCBSR_LENGTH_B,
        ISNULL(q.PCBS_MARK1_POS_X, '-') AS PCBSR_MARK1_POS_X_B,
        ISNULL(q.PCBS_MARK1_POS_Y, '-') AS PCBSR_MARK1_POS_Y_B
    FROM (
        SELECT r.*, OLBSI_RECIPEID, SI_RECIPEID, SS_RECIPEID, 
            PB01_RECIPEID, PB02_RECIPEID, CP_RECIPEID, SI, pcbs, ag, JIG
        FROM (
            SELECT cc.*, dd.FAB 
            FROM (
                SELECT [EQPID], [PRODUCT], SUM([OUT]) AS OUT, MAX([DATE]) AS MAX_DATE
                FROM [Out_new].[dbo].[OUT_HH]
                WHERE [OUT] > 0 AND SUBSTRING([EQPID], 3, 2) = 'OL'
                GROUP BY [EQPID], [PRODUCT]
            ) AS cc
            LEFT JOIN fixplan_info AS dd ON cc.[PRODUCT] = dd.[PRODUCTID]
        ) AS r
        LEFT JOIN [product].[dbo].[ppid_mod] AS s ON LEFT(r.[EQPID], 1) = s.MOD AND r.PRODUCT = s.PRODUCT
        LEFT JOIN [product].[dbo].[jig] AS t ON r.[PRODUCT] = t.[PRODUCT]
    ) AS m
    LEFT JOIN bom_info AS n ON m.[PRODUCT] = n.[PRODUCTID]
    LEFT JOIN panel_size_info AS o ON m.[PRODUCT] = o.[MATCODE]
    LEFT JOIN pcb_size_pcbs AS p ON m.[PRODUCT] = p.[PRODUCT]
    LEFT JOIN pcb_size_pcbsr AS q ON m.[PRODUCT] = q.[PRODUCT]
)

-- step ppid_vs
DELETE FROM [product].[dbo].[ppid_vs]
INSERT INTO [product].[dbo].[ppid_vs]
SELECT 
    a.[MOD] AS MOD_A,
    a.[PRODUCT] AS PRODUCT_A,
    a.FAB AS FAB_A,
    a.[JIG] AS JIG_A,
    a.[OLBSI_RECIPEID] AS OLBSI_RECIPEID_A,
    a.[SI_RECIPEID] AS SI_RECIPEID_A,
    a.[SS_RECIPEID] AS SS_RECIPEID_A,
    a.[PB01_RECIPEID] AS PB01_RECIPEID_A,
    a.[PB02_RECIPEID] AS PB02_RECIPEID_A,
    a.[CP_RECIPEID] AS CP_RECIPEID_A,
    a.[SI] AS SI_A,
    a.[pcbs] AS pcbs_A,
    a.[ag] AS ag_A,
    a.OLB_A,
    a.PB_A,
    a.PANEL_SIZE_X_A,
    a.PANEL_SIZE_Y_A,
    a.PANEL_XD_L_Mark_Pos_X_A,
    a.PANEL_XD_L_Mark_Pos_Y_A,
    a.PANEL_XU_L_Mark_Pos_X_A,
    a.PANEL_XU_L_Mark_Pos_Y_A,
    a.PANEL_XU_PITCH_1_A,
    a.PANEL_XU_PITCH_2_A,
    a.PANEL_XU_PITCH_3_A,
    a.PCBS_HEIGHT_A,
    a.PCBS_LENGTH_A,
    a.PCBS_MARK1_POS_X_A,
    a.PCBS_MARK1_POS_Y_A,
    a.PCBSR_HEIGHT_A,
    a.PCBSR_LENGTH_A,
    a.PCBSR_MARK1_POS_X_A,
    a.PCBSR_MARK1_POS_Y_A,
    b.EQPID,
    b.MAX_DATE,
    b.OUT,
    b.[MOD] AS MOD_B,
    b.[PRODUCT] AS PRODUCT_B,
    b.FAB AS FAB_B,
    b.[JIG] AS JIG_B,
    b.[OLBSI_RECIPEID] AS OLBSI_RECIPEID_B,
    b.[SI_RECIPEID] AS SI_RECIPEID_B,
    b.[SS_RECIPEID] AS SS_RECIPEID_B,
    b.[PB01_RECIPEID] AS PB01_RECIPEID_B,
    b.[PB02_RECIPEID] AS PB02_RECIPEID_B,
    b.[CP_RECIPEID] AS CP_RECIPEID_B,
    b.[SI] AS SI_B,
    b.[pcbs] AS pcbs_B,
    b.[ag] AS ag_B,
    b.OLB_B,
    b.PB_B,
    b.PANEL_SIZE_X_B,
    b.PANEL_SIZE_Y_B,
    b.PANEL_XD_L_Mark_Pos_X_B,
    b.PANEL_XD_L_Mark_Pos_Y_B,
    b.PANEL_XU_L_Mark_Pos_X_B,
    b.PANEL_XU_L_Mark_Pos_Y_B,
    b.PANEL_XU_PITCH_1_B,
    b.PANEL_XU_PITCH_2_B,
    b.PANEL_XU_PITCH_3_B,
    b.PCBS_HEIGHT_B,
    b.PCBS_LENGTH_B,
    b.PCBS_MARK1_POS_X_B,
    b.PCBS_MARK1_POS_Y_B,
    b.PCBSR_HEIGHT_B,
    b.PCBSR_LENGTH_B,
    b.PCBSR_MARK1_POS_X_B,
    b.PCBSR_MARK1_POS_Y_B,
    -- 参数比较字段
    CASE WHEN a.[OLBSI_RECIPEID] = b.[OLBSI_RECIPEID] THEN 1 ELSE 0 END AS D_OLBSI_RECIPEID,
    CASE WHEN a.[SI_RECIPEID] = b.[SI_RECIPEID] THEN 1 ELSE 0 END AS D_SI_RECIPEID,
    CASE WHEN a.[SS_RECIPEID] = b.[SS_RECIPEID] THEN 1 ELSE 0 END AS D_SS_RECIPEID,
    CASE WHEN a.[PB01_RECIPEID] = b.[PB01_RECIPEID] AND a.[PB02_RECIPEID] = b.[PB02_RECIPEID] THEN 1 ELSE 0 END AS D_PB_RECIPEID,
    CASE WHEN a.[CP_RECIPEID] = b.[CP_RECIPEID] THEN 1 ELSE 0 END AS D_CP_RECIPEID,
    -- 总体相似度计算
    CASE WHEN a.[OLBSI_RECIPEID] = b.[OLBSI_RECIPEID] THEN 1 ELSE 0 END +
    CASE WHEN a.[SI_RECIPEID] = b.[SI_RECIPEID] THEN 1 ELSE 0 END +
    CASE WHEN a.[SS_RECIPEID] = b.[SS_RECIPEID] THEN 1 ELSE 0 END +
    CASE WHEN a.[PB01_RECIPEID] = b.[PB01_RECIPEID] AND a.[PB02_RECIPEID] = b.[PB02_RECIPEID] THEN 1 ELSE 0 END AS SAME,
    CASE WHEN a.[CP_RECIPEID] = b.[CP_RECIPEID] THEN 1 ELSE 0 END AS SAME_C,
    -- 产品型号相似度
    CASE 
        WHEN SUBSTRING(a.[PRODUCT], 4, 8) = SUBSTRING(b.[PRODUCT], 4, 8) THEN 1
        WHEN SUBSTRING(a.[PRODUCT], 4, 7) = SUBSTRING(b.[PRODUCT], 4, 7) THEN 2
        WHEN SUBSTRING(a.[PRODUCT], 4, 5) = SUBSTRING(b.[PRODUCT], 4, 5) THEN 3
        WHEN SUBSTRING(a.[PRODUCT], 4, 3) = SUBSTRING(b.[PRODUCT], 4, 3) THEN 4
        ELSE 88
    END AS ss,
    -- 面板参数比较
    CASE WHEN a.PANEL_SIZE_X_A = b.PANEL_SIZE_X_B AND a.PANEL_SIZE_Y_A = b.PANEL_SIZE_Y_B THEN 1 ELSE 0 END AS D_PANEL_SIZE,
    CASE WHEN a.PANEL_XD_L_Mark_Pos_X_A = b.PANEL_XD_L_Mark_Pos_X_B AND a.PANEL_XD_L_Mark_Pos_Y_A = b.PANEL_XD_L_Mark_Pos_Y_B THEN 1 ELSE 0 END AS D_PANEL_XD_L_Mark_Pos,
    CASE WHEN a.PANEL_XU_L_Mark_Pos_X_A = b.PANEL_XU_L_Mark_Pos_X_B AND a.PANEL_XU_L_Mark_Pos_Y_A = b.PANEL_XU_L_Mark_Pos_Y_B THEN 1 ELSE 0 END AS D_PANEL_XU_L_Mark_Pos,
    CASE WHEN a.PANEL_XU_PITCH_1_A = b.PANEL_XU_PITCH_1_B AND a.PANEL_XU_PITCH_2_A = b.PANEL_XU_PITCH_2_B AND a.PANEL_XU_PITCH_3_A = b.PANEL_XU_PITCH_3_B THEN 1 ELSE 0 END AS D_PANEL_XU_PITCH,
    -- PCB参数比较
    CASE WHEN a.PCBS_HEIGHT_A = b.PCBS_HEIGHT_B THEN 1 ELSE 0 END AS D_PCBSL_HEIGHT,
    CASE WHEN a.PCBS_LENGTH_A = b.PCBS_LENGTH_B AND a.PCBSR_HEIGHT_A = b.PCBSR_HEIGHT_B THEN 1 ELSE 0 END AS D_PCBSR_HEIGHT,
    CASE WHEN a.PCBS_LENGTH_A = b.PCBS_LENGTH_B THEN 1 ELSE 0 END AS D_PCBSL_LENGTH,
    CASE WHEN a.PCBS_LENGTH_A = b.PCBS_LENGTH_B AND a.PCBSR_LENGTH_A = b.PCBSR_LENGTH_B THEN 1 ELSE 0 END AS D_PCBSR_LENGTH,
    CASE WHEN a.PCBS_MARK1_POS_X_A = b.PCBS_MARK1_POS_X_B THEN 1 ELSE 0 END AS D_PCBSL_MARK1_POS_XD,
    CASE WHEN a.PCBS_MARK1_POS_X_A = b.PCBS_MARK1_POS_X_B AND a.PCBSR_MARK1_POS_X_A = b.PCBSR_MARK1_POS_X_B THEN 1 ELSE 0 END AS D_PCBSR_MARK1_POS_XD,
    CASE WHEN a.PCBS_MARK1_POS_Y_A = b.PCBS_MARK1_POS_Y_B THEN 1 ELSE 0 END AS D_PCBSL_MARK1_POS_YD,
    CASE WHEN a.PCBS_MARK1_POS_Y_A = b.PCBS_MARK1_POS_Y_B AND a.PCBSR_MARK1_POS_Y_A = b.PCBSR_MARK1_POS_Y_B THEN 1 ELSE 0 END AS D_PCBSR_MARK1_POS_YD
FROM product_a_data AS a
CROSS JOIN product_b_data AS b
WHERE a.[MOD] = b.[MOD] 
    AND LEFT(a.[PRODUCT], 1) NOT IN ('L', 'M') 
    AND LEFT(b.[PRODUCT], 1) NOT IN ('L', 'M')