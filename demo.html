<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理系统 - Ant Design 演示</title>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f0f2f5;
        }
        .header {
            background: #001529;
            color: white;
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .logo {
            font-size: 20px;
            font-weight: bold;
            margin-right: 24px;
        }
        .content {
            padding: 24px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .card-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #262626;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 8px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #52c41a; }
        .status-offline { background-color: #ff4d4f; }
        .status-warning { background-color: #faad14; }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🏭 设备综合管理系统</div>
        <div style="flex: 1;"></div>
        <div id="user-info">加载中...</div>
    </div>

    <div class="content">
        <!-- 系统状态卡片 -->
        <div class="card">
            <div class="card-title">系统状态监控</div>
            <div id="system-status">
                <div style="text-align: center; padding: 20px; color: #666;">
                    正在加载系统状态...
                </div>
            </div>
        </div>

        <div class="grid">
            <!-- 设备交接管理 -->
            <div class="card">
                <div class="card-title">设备交接管理</div>
                <div id="associate-management">
                    <div style="text-align: center; padding: 20px; color: #666;">
                        正在加载交接数据...
                    </div>
                </div>
            </div>

            <!-- 故障统计 -->
            <div class="card">
                <div class="card-title">故障统计分析</div>
                <div id="fault-statistics">
                    <div style="text-align: center; padding: 20px; color: #666;">
                        正在加载故障数据...
                    </div>
                </div>
            </div>

            <!-- 备品库存 -->
            <div class="card">
                <div class="card-title">备品库存状态</div>
                <div id="spareparts-inventory">
                    <div style="text-align: center; padding: 20px; color: #666;">
                        正在加载库存数据...
                    </div>
                </div>
            </div>

            <!-- 参数监控 -->
            <div class="card">
                <div class="card-title">实时参数监控</div>
                <div id="parameter-monitoring">
                    <div style="text-align: center; padding: 20px; color: #666;">
                        正在加载监控数据...
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据图表展示 -->
        <div class="card">
            <div class="card-title">数据可视化图表</div>
            <div id="charts-container">
                <div style="text-align: center; padding: 40px; color: #666;">
                    正在加载图表组件...
                </div>
            </div>
        </div>
    </div>

    <!-- 依赖库引入 -->
    <script src="lib/react.production.min.js"></script>
    <script src="lib/react-dom.production.min.js"></script>
    <script src="lib/lodash.min.js"></script>
    <script src="lib/dayjs.min.js"></script>
    <script src="lib/antd/antd.min.js"></script>
    <script src="lib/antd-charts/charts.min.js"></script>

    <script>
        // 模拟数据
        const mockData = {
            systemStatus: [
                { name: '生产线A', status: 'online', value: '正常运行' },
                { name: '生产线B', status: 'warning', value: '维护中' },
                { name: '生产线C', status: 'online', value: '正常运行' },
                { name: '生产线D', status: 'offline', value: '停机检修' }
            ],
            associates: [
                { id: 1, line: 'A1', issue: '设备异响', status: 'open', reporter: '张三' },
                { id: 2, line: 'B2', issue: '温度异常', status: 'closed', reporter: '李四' },
                { id: 3, line: 'C1', issue: '压力不稳', status: 'open', reporter: '王五' }
            ],
            faults: [
                { type: '机械故障', count: 15, trend: 'up' },
                { type: '电气故障', count: 8, trend: 'down' },
                { type: '软件故障', count: 3, trend: 'stable' }
            ],
            spareparts: [
                { name: '轴承', stock: 25, min: 10, status: 'sufficient' },
                { name: '密封圈', stock: 5, min: 15, status: 'insufficient' },
                { name: '传感器', stock: 12, min: 8, status: 'sufficient' }
            ]
        };

        // 等待页面加载完成
        window.addEventListener('load', function() {
            setTimeout(initializeApp, 500);
        });

        function initializeApp() {
            try {
                if (!window.antd || !window.React || !window.ReactDOM) {
                    throw new Error('必要的库未加载完成');
                }

                // 渲染各个模块
                renderUserInfo();
                renderSystemStatus();
                renderAssociateManagement();
                renderFaultStatistics();
                renderSparepartsInventory();
                renderParameterMonitoring();
                renderCharts();

            } catch (error) {
                console.error('应用初始化失败:', error);
                document.body.innerHTML = `
                    <div style="padding: 40px; text-align: center; color: #ff4d4f;">
                        <h2>应用加载失败</h2>
                        <p>错误信息: ${error.message}</p>
                        <p>请检查浏览器控制台获取详细信息</p>
                    </div>
                `;
            }
        }

        function renderUserInfo() {
            const { Space, Avatar, Dropdown } = antd;
            
            const UserInfo = React.createElement(Space, null,
                React.createElement(Avatar, { 
                    style: { backgroundColor: '#1890ff' } 
                }, '管'),
                React.createElement('span', null, '管理员')
            );

            ReactDOM.render(UserInfo, document.getElementById('user-info'));
        }

        function renderSystemStatus() {
            const { Row, Col, Card, Badge } = antd;
            
            const StatusCards = React.createElement(Row, { gutter: [16, 16] },
                ...mockData.systemStatus.map((item, index) => 
                    React.createElement(Col, { span: 6, key: index },
                        React.createElement(Card, { size: 'small' },
                            React.createElement('div', { style: { textAlign: 'center' } },
                                React.createElement('div', null,
                                    React.createElement('span', { 
                                        className: `status-indicator status-${item.status}` 
                                    }),
                                    item.name
                                ),
                                React.createElement('div', { 
                                    style: { marginTop: 8, fontSize: 12, color: '#666' } 
                                }, item.value)
                            )
                        )
                    )
                )
            );

            ReactDOM.render(StatusCards, document.getElementById('system-status'));
        }

        function renderAssociateManagement() {
            const { Table, Tag } = antd;
            
            const columns = [
                { title: '产线', dataIndex: 'line', key: 'line', width: 60 },
                { title: '问题描述', dataIndex: 'issue', key: 'issue' },
                { 
                    title: '状态', 
                    dataIndex: 'status', 
                    key: 'status',
                    render: (status) => React.createElement(Tag, {
                        color: status === 'open' ? 'red' : 'green'
                    }, status === 'open' ? '待处理' : '已完成')
                }
            ];

            const AssociateTable = React.createElement(Table, {
                columns: columns,
                dataSource: mockData.associates,
                pagination: false,
                size: 'small'
            });

            ReactDOM.render(AssociateTable, document.getElementById('associate-management'));
        }

        function renderFaultStatistics() {
            const { Statistic, Row, Col } = antd;
            
            const FaultStats = React.createElement(Row, { gutter: 16 },
                ...mockData.faults.map((fault, index) => 
                    React.createElement(Col, { span: 8, key: index },
                        React.createElement(Statistic, {
                            title: fault.type,
                            value: fault.count,
                            suffix: '次',
                            valueStyle: { 
                                color: fault.trend === 'up' ? '#cf1322' : 
                                       fault.trend === 'down' ? '#3f8600' : '#666'
                            }
                        })
                    )
                )
            );

            ReactDOM.render(FaultStats, document.getElementById('fault-statistics'));
        }

        function renderSparepartsInventory() {
            const { Progress, Space } = antd;
            
            const InventoryList = React.createElement('div', null,
                ...mockData.spareparts.map((part, index) => 
                    React.createElement('div', { 
                        key: index, 
                        style: { marginBottom: 12 } 
                    },
                        React.createElement('div', { 
                            style: { 
                                display: 'flex', 
                                justifyContent: 'space-between',
                                marginBottom: 4
                            } 
                        },
                            React.createElement('span', null, part.name),
                            React.createElement('span', null, `${part.stock}/${part.min}`)
                        ),
                        React.createElement(Progress, {
                            percent: Math.round((part.stock / part.min) * 100),
                            status: part.status === 'insufficient' ? 'exception' : 'success',
                            size: 'small'
                        })
                    )
                )
            );

            ReactDOM.render(InventoryList, document.getElementById('spareparts-inventory'));
        }

        function renderParameterMonitoring() {
            const { Alert, Space } = antd;
            
            const MonitoringPanel = React.createElement(Space, { 
                direction: 'vertical', 
                style: { width: '100%' } 
            },
                React.createElement(Alert, {
                    message: '实时监控',
                    description: '所有设备参数正常，无异常报警',
                    type: 'success',
                    showIcon: true
                }),
                React.createElement('div', { 
                    style: { 
                        display: 'grid', 
                        gridTemplateColumns: '1fr 1fr',
                        gap: '8px',
                        fontSize: '12px'
                    } 
                },
                    React.createElement('div', null, '温度: 25.6°C'),
                    React.createElement('div', null, '压力: 1.2MPa'),
                    React.createElement('div', null, '湿度: 45%'),
                    React.createElement('div', null, '振动: 0.8mm/s')
                )
            );

            ReactDOM.render(MonitoringPanel, document.getElementById('parameter-monitoring'));
        }

        function renderCharts() {
            const chartsContainer = document.getElementById('charts-container');
            
            if (window.Charts) {
                chartsContainer.innerHTML = `
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                        <div>
                            <h4 style="margin-bottom: 16px;">设备运行趋势</h4>
                            <div style="height: 200px; background: #fafafa; border: 1px dashed #d9d9d9; 
                                       display: flex; align-items: center; justify-content: center; color: #666;">
                                折线图区域<br>
                                (Charts库已加载)
                            </div>
                        </div>
                        <div>
                            <h4 style="margin-bottom: 16px;">故障分布统计</h4>
                            <div style="height: 200px; background: #fafafa; border: 1px dashed #d9d9d9; 
                                       display: flex; align-items: center; justify-content: center; color: #666;">
                                饼图区域<br>
                                (Charts库已加载)
                            </div>
                        </div>
                    </div>
                `;
            } else {
                chartsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #ff4d4f;">
                        图表库未正确加载
                    </div>
                `;
            }
        }

        // 错误处理
        window.addEventListener('error', function(event) {
            console.error('页面错误:', event.error);
        });
    </script>
</body>
</html>
