# JIG数据管理功能设置指南

## 功能概述
本功能实现了JIG数据的数据库查询和管理，替换了原有的硬编码模拟数据。

## 数据库配置
- **服务器**: localhost
- **用户名**: root
- **密码**: （空）
- **数据库名**: equipment_management
- **表名**: jig
- **字段**: LINE、MODEL、JIG

## 安装步骤

### 1. 确保环境准备
- XAMPP已安装并启动（Apache + MySQL）
- 数据库 `equipment_management` 已创建
- PHP扩展 `mysqli` 已启用

### 2. 数据库初始化
有三种方式初始化数据库表：

#### 方式一：使用PHP脚本（推荐）
在浏览器中访问：
```
http://localhost/equipment_management/ModelChange/init_jig_table.php
```

#### 方式二：使用测试页面
在浏览器中访问：
```
http://localhost/equipment_management/ModelChange/test_jig_api.html
```
点击"测试数据库连接"按钮

#### 方式三：手动执行SQL
在MySQL中执行 `create_jig_table.sql` 文件中的SQL语句。

### 3. 验证安装
1. 打开测试页面验证API功能正常
2. 打开 `ModelChange.html` 页面
3. 在JIG信息区域点击"查询"按钮
4. 应该能看到从数据库加载的数据

## 功能特性

### 1. 查询功能
- **模糊搜索**: 支持LINE、MODEL、JIG字段的模糊匹配
- **组合查询**: 可以同时使用多个字段进行查询
- **实时反馈**: 显示查询结果数量和状态

### 2. 用户界面
- **默认状态**: 页面加载时显示"请点击查询按钮获取数据"
- **加载状态**: 查询时显示加载动画
- **结果显示**: 以表格形式展示查询结果
- **消息提示**: 右上角显示操作结果消息

### 3. 操作按钮
- **查询按钮**: 根据搜索条件查询数据
- **重置按钮**: 清空搜索条件并重置表格

## 文件结构
```
ModelChange/
├── ModelChange.html          # 主页面文件（已修改）
├── ModelChange.css          # 样式文件（已更新）
├── ModelChange.js           # 原有功能脚本
├── jig_functions.js         # 新增JIG查询功能脚本
├── get_jig_data.php         # JIG数据查询API
├── init_jig_table.php       # 数据库表初始化脚本
├── create_jig_table.sql     # 数据库表创建SQL
├── test_jig_api.html        # API功能测试页面
└── JIG_SETUP_README.md      # 本说明文件
```

## API接口

### get_jig_data.php
**功能**: 查询JIG数据
**方法**: GET
**参数**:
- `line`: LINE字段模糊搜索（可选）
- `model`: MODEL字段模糊搜索（可选）
- `jig`: JIG字段模糊搜索（可选）

**返回格式**:
```json
{
    "success": true,
    "data": [
        {
            "LINE": "L01",
            "MODEL": "MODEL-A001",
            "JIG": "JIG-001"
        }
    ],
    "count": 1,
    "message": "查询成功"
}
```

## 使用说明

### 基本查询
1. 在LINE、MODEL、JIG输入框中输入搜索条件
2. 点击"查询"按钮执行搜索
3. 查看表格中的结果

### 重置搜索
1. 点击"重置"按钮
2. 搜索条件将被清空
3. 表格恢复到默认状态

### 快捷操作
- 在任意搜索框中按回车键可直接执行查询
- 支持空条件查询（返回所有数据）

## 技术特性

### 安全性
- SQL注入防护（使用预处理语句）
- XSS攻击防护（HTML转义）
- 输入验证和清理

### 性能优化
- 数据库索引优化
- 前端加载状态提示
- 错误处理和日志记录

### 用户体验
- 响应式设计
- 实时消息反馈
- 直观的操作界面
- 键盘快捷键支持

## 故障排除

### 常见问题
1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库配置信息

2. **表不存在**
   - 运行 `init_jig_table.php` 初始化表
   - 或手动执行SQL创建表

3. **查询无结果**
   - 检查数据库中是否有数据
   - 验证搜索条件是否正确

4. **页面报错**
   - 检查浏览器控制台错误信息
   - 查看PHP错误日志

### 调试方法
1. 打开浏览器开发者工具
2. 查看Network标签页的请求响应
3. 检查Console标签页的错误信息
4. 验证数据库连接和数据

## 维护说明

### 数据维护
- 可通过数据库管理工具直接编辑jig表数据
- 支持标准的增删改查操作

### 代码维护
- 主要逻辑在 `jig_functions.js` 中
- API接口在 `get_jig_data.php` 中
- 样式定义在 `ModelChange.css` 中

## 扩展建议

### 功能扩展
1. 添加数据编辑功能
2. 实现数据导入导出
3. 增加数据统计分析
4. 添加操作日志记录

### 性能优化
1. 实现分页查询
2. 添加缓存机制
3. 优化数据库查询
4. 前端虚拟滚动
